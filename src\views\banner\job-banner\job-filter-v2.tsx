'use client';

import React, { Suspense, useEffect, useMemo, useRef, useState } from 'react';
import { useAtom } from 'jotai';
import {
  currentPageAtom,
  difficultyAtom,
  categoriesAtom,
  workTypeAtom,
  resetFiltersAtom,
  salaryAtom,
  titleAtom,
  locationAtom,
  workPlace<PERSON>tom,
  levelsAtom,
} from '@/store/job-atom';
import {
  MultiSelect,
  MultiSelectOption,
  Checkbox,
  Input,
  Tooltip,
  Button,
} from 'rizzui';
import cn from '@/utils/class-names';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useURLParamsManager } from '@/hooks/use-update-url-params';
import CloseIcon from '@/views/icons/close';
import JobFilterModal from './job-filter-modal';
import FilterIcon from '@/views/icons/filter';

const BRAND = '#0D1321';

interface Option {
  label: string;
  value: string;
  description?: string;
}
interface ChipItem {
  label: string;
  value: string;
}

interface MergedChipItem extends ChipItem {
  category: string;
  onRemove: (value: string) => void;
  onClearCategory: () => void;
}

export const filterOptions = {
  level: [
    { label: 'Internship', value: 'internship' },
    { label: 'Fresher', value: 'fresher' },
    { label: 'Junior', value: 'junior' },
    { label: 'Middle', value: 'middle' },
    { label: 'Senior', value: 'senior' },
    { label: 'Lead', value: 'lead' },
    { label: 'Principal Engineer', value: 'principalengineer' },
    { label: 'Engineering Manager', value: 'engineeringmanager' },
    { label: 'Director of Engineering', value: 'directorofengineering' },
    { label: 'CTO', value: 'cto' },
  ],
  industry: [
    { label: 'Software Development', value: 'softwaredevelopment' },
    { label: 'Web Development', value: 'webdevelopment' },
    { label: 'Mobile App Development', value: 'mobileappdevelopment' },
    { label: 'Game Development', value: 'gamedevelopment' },
    {
      label: 'DevOps & Cloud Engineering',
      value: 'devopscloudengineering',
    },
    {
      label: 'Data Science & Machine Learning',
      value: 'datasciencemachinelearning',
    },
    { label: 'Artificial Intelligence', value: 'artificialintelligence' },
    { label: 'Cybersecurity', value: 'cybersecurity' },
    { label: 'Blockchain & Web3', value: 'blockchainweb3' },
    { label: 'UI/UX Design', value: 'uiuxdesign' },
    { label: 'Product Management', value: 'productmanagement' },
    { label: 'IT Project Management', value: 'itprojectmanagement' },
    {
      label: 'Quality Assurance & Testing',
      value: 'qualityassurancetesting',
    },
    { label: 'Database Administration', value: 'databaseadministration' },
    { label: 'Network Engineering', value: 'networkengineering' },
    { label: 'Embedded Systems & IoT', value: 'embeddedsystemsiot' },
    { label: 'AR/VR Development', value: 'arvrdevelopment' },
    { label: 'IT Support & Helpdesk', value: 'itsupporthelpdesk' },
    { label: 'Systems Administration', value: 'systemsadministration' },
    {
      label: 'Tech Writing & Documentation',
      value: 'techwritingdocumentation',
    },
  ],
  workType: [
    { label: 'Full-time', value: 'full_time' },
    { label: 'Part-time', value: 'part_time' },
    { label: 'Contract', value: 'contract' },
  ],
  workPlace: [
    { label: 'Onsite', value: 'onsite' },
    { label: 'Remote', value: 'remote' },
    { label: 'Hybrid', value: 'hybrid' },
  ],
  simulationLevel: [
    { label: 'Beginner', value: '1' },
    { label: 'Intermediate', value: '2' },
    { label: 'Advanced', value: '3' },
  ],
};

// ---------- utilities ----------
const idsToChipItems = (ids: string[], list: Option[]): ChipItem[] =>
  ids
    .map((id) => {
      const o = list.find((x) => x.value === id);
      return o ? { label: o.label, value: o.value } : undefined;
    })
    .filter(Boolean) as ChipItem[];

// --- BrandSlider (discrete 1..3), a11y + keyboard, brand fill ---
function BrandSlider({
  value,
  onChange,
  disabled,
}: {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
}) {
  const percent = ((Math.min(3, Math.max(1, value)) - 1) / 2) * 100; // 0, 50, 100
  return (
    <div
      className={cn(
        'brandRange relative w-full select-none',
        disabled && 'opacity-60'
      )}
    >
      <input
        type="range"
        min={1}
        max={3}
        step={1}
        aria-label="Difficulty"
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        disabled={disabled}
        className="h-3 w-full cursor-pointer appearance-none rounded-full bg-transparent focus:outline-none"
        style={{
          // custom property consumed by CSS below to paint the filled track
          ['--percent' as any]: `${percent}%`,
          ['--brand' as any]: BRAND,
        }}
      />
      {/* <div className="mt-2 grid grid-cols-3 text-[12px] text-slate-500">
        <span className="text-left">Beginner</span>
        <span className="text-center">Intermediate</span>
        <span className="text-right">Advanced</span>
      </div> */}
      <style jsx>{`
        .brandRange input[type='range']::-webkit-slider-runnable-track {
          height: 8px;
          border-radius: 9999px;
          background: linear-gradient(
            to right,
            var(--brand) 0%,
            var(--brand) var(--percent),
            #e5e7eb var(--percent),
            #e5e7eb 100%
          );
        }
        .brandRange input[type='range']::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          height: 18px;
          width: 18px;
          border-radius: 9999px;
          background: var(--brand);
          border: 3px solid #fff;
          margin-top: -5px;
          box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
        }
        .brandRange input[type='range']::-moz-range-track {
          height: 8px;
          border-radius: 9999px;
          background: linear-gradient(
            to right,
            var(--brand) 0%,
            var(--brand) var(--percent),
            #e5e7eb var(--percent),
            #e5e7eb 100%
          );
        }
        .brandRange input[type='range']::-moz-range-thumb {
          height: 18px;
          width: 18px;
          border-radius: 9999px;
          background: var(--brand);
          border: 3px solid #fff;
          box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
        }
        .brandRange input[type='range']:focus-visible::-webkit-slider-thumb {
          outline: 3px solid rgba(13, 19, 33, 0.35);
          outline-offset: 2px;
        }
        .brandRange input[type='range']:focus-visible::-moz-range-thumb {
          outline: 3px solid rgba(13, 19, 33, 0.35);
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
}

function MergedChips({
  items,
  onClear,
  totalSelected,
}: {
  items: MergedChipItem[];
  onClear: () => void;
  totalSelected: number;
}) {
  return (
    <div className="flex items-center gap-2">
      <span className="select-none whitespace-nowrap text-xs font-medium text-gray-600">
        Active filters:
      </span>
      {totalSelected ? (
        <>
          <div className="min-w-0 flex-1">
            <div className="flex flex-wrap items-center gap-2">
              {items.map((chip) => (
                <Tooltip
                  key={`${chip.category}-${chip.value}`}
                  content={chip.category}
                  color="invert"
                >
                  <span className="group inline-flex items-center gap-1 rounded-full border border-[#c3c3c3] bg-white/80 px-2 py-0.5 text-[12px] leading-5 shadow-sm">
                    <span className="max-w-[12rem] truncate">{chip.label}</span>
                    <button
                      onClick={() => chip.onRemove(chip.value)}
                      className="-mr-1 rounded-full p-0.5 text-[#c3c3c3] hover:bg-slate-200 hover:text-slate-600"
                      aria-label={`Remove ${chip.label}`}
                    >
                      <CloseIcon className="h-3 w-3" />
                    </button>
                  </span>
                </Tooltip>
              ))}
            </div>
          </div>

          {/* Clear buttons for each category */}
          <div className="flex items-center gap-1" color="invert">
            <button
              className="whitespace-nowrap rounded-md text-sm text-slate-700 underline hover:text-primary"
              onClick={onClear}
            >
              Clear all
            </button>
          </div>
        </>
      ) : (
        <span className="select-none whitespace-nowrap text-xs font-medium text-gray-600">
          none
        </span>
      )}
    </div>
  );
}

// ============================ MAIN ============================
interface IProps {
  isHomePage?: boolean;
  searchRef?: React.RefObject<HTMLInputElement>;
  locationRef?: React.RefObject<HTMLInputElement>;
}

function JobFilterContent({ isHomePage, searchRef, locationRef }: IProps) {
  // atoms
  const [workTypes, setWorkTypes] = useAtom(workTypeAtom);
  const [workPlaces, setWorkPlaces] = useAtom(workPlaceAtom);
  const [difficulty, setDifficulty] = useAtom(difficultyAtom);
  const [salary, setSalary] = useAtom(salaryAtom);
  const [categories, setCategories] = useAtom(categoriesAtom);
  const [levels, setLevels] = useAtom(levelsAtom);
  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, setPage] = useAtom(currentPageAtom);
  const [, setTitle] = useAtom(titleAtom);
  const [, setLocation] = useAtom(locationAtom);

  // routing
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const urlSearchParams = useSearchParams();
  const urlManager = useURLParamsManager();

  // UI state
  const [mobileOpen] = useState(false); // (kept for possible reuse)
  const [difficultyEnabled, setDifficultyEnabled] = useState<boolean>(
    Boolean(
      urlSearchParams.get('level') ||
        (Array.isArray(difficulty) && difficulty.length)
    )
  );
  const [openFilterModal, setOpenFilterModal] = useState(false);

  // ref
  const minSalaryRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;
  const maxSalaryRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;
  const industryRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;
  const levelRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;

  const totalSelected =
    workTypes.length +
    workPlaces.length +
    salary.length +
    (categories ? 1 : 0) +
    (difficultyEnabled ? difficulty.length : 0);

  const syncUrl = (key: string, value: string[] | string) => {
    urlManager.update({ [key]: value as any, page: 1 });
  };

  const handleFilterChange =
    (setter: (value: string[]) => void) =>
    (value: string[], paramKey: string) => {
      setter(value);
      setPage(1);
      syncUrl(paramKey, value);
    };

  const trueClearAll = () => {
    resetFilters();
    setWorkTypes([]);
    setWorkPlaces([]);
    setDifficulty([]);
    setSalary([]);
    setCategories([]);
    setLevels([]);
    setDifficultyEnabled(false);
    setTitle('');
    setLocation('');
    searchRef!.current!.value = '';
    locationRef!.current!.value = '';
    if (minSalaryRef.current) minSalaryRef.current.value = '';
    if (maxSalaryRef.current) maxSalaryRef.current.value = '';
    if (industryRef.current) industryRef.current.value = '';
    setPage(1);
    urlManager.clear();
    if (searchInputRef.current) searchInputRef.current.value = '';
  };

  // hydrate from URL
  useEffect(() => {
    const categories = urlSearchParams.get('categories') || '';
    const levels = urlSearchParams.get('levels') || '';
    const workType = urlSearchParams.get('workType') || '';
    const workPlace = urlSearchParams.get('workPlace') || '';
    const salary = urlSearchParams.get('salary') || '';
    const level = urlSearchParams.get('level') || '';
    const pageFromUrl = parseInt(urlSearchParams.get('page') || '1');
    const salaries = salary.split(',');

    setWorkTypes(workType ? workType.split(',') : []);
    setWorkPlaces(workPlace ? workPlace.split(',') : []);
    setDifficulty(level ? level.split(',') : []);
    setSalary(salary ? salary.split(',') : []);
    if (minSalaryRef.current) {
      minSalaryRef.current.value = salaries.length ? salaries[0] : '';
    }
    if (maxSalaryRef.current) {
      maxSalaryRef.current.value = salaries.length > 1 ? salaries[1] : '';
    }
    setCategories(categories ? categories.split(',') : []);
    setLevels(levels ? levels.split(',') : []);
    setPage(pageFromUrl || 1);
    if (level) setDifficultyEnabled(true);
  }, [
    urlSearchParams,
    setWorkTypes,
    setWorkPlaces,
    setDifficulty,
    setSalary,
    setCategories,
    setLevels,
    setPage,
  ]);

  useEffect(() => {
    if (pathname === '/') {
      if (
        workTypes.length ||
        workPlaces.length ||
        salary.length ||
        categories.length ||
        levels.length ||
        (difficultyEnabled && difficulty.length)
      ) {
        router.push('/find-jobs');
      }
    }
  }, [
    pathname,
    workTypes,
    workPlaces,
    salary,
    categories,
    levels,
    difficultyEnabled,
    difficulty,
    router,
  ]);

  useEffect(() => {
    let timeout = null;
    if (openFilterModal) {
      timeout = setTimeout(() => {
        if (minSalaryRef.current && maxSalaryRef.current) {
          if (salary.length >= 1) {
            minSalaryRef.current!.value = salary[0];
          } else {
            minSalaryRef.current!.value = '';
          }
          if (salary.length >= 2) {
            maxSalaryRef.current!.value = salary[1];
          } else {
            maxSalaryRef.current!.value = '';
          }
        }
      }, 100);
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [
    openFilterModal,
    salary,
    minSalaryRef.current,
    maxSalaryRef.current,
    industryRef.current,
    levelRef.current,
  ]);

  // chips
  const workTypeItems = useMemo(
    () => idsToChipItems(workTypes, filterOptions.workType),
    [workTypes]
  );
  const workPlaceItems = useMemo(
    () => idsToChipItems(workPlaces, filterOptions.workPlace),
    [workPlaces]
  );
  const salaryItems = useMemo(() => {
    if (salary.length === 0) return [];

    if (salary.length === 1) {
      return [{ label: salary[0], value: salary[0] }];
    } else if (salary.length === 2) {
      return [
        {
          label: `${salary[0]}-${salary[1]}`,
          value: `${salary[0]}-${salary[1]}`,
        },
      ];
    }
    return [];
  }, [salary]);

  const difficultyItems = useMemo(
    () =>
      difficultyEnabled
        ? idsToChipItems(difficulty, filterOptions.simulationLevel)
        : [],
    [difficulty, difficultyEnabled]
  );
  const industryItems = useMemo(
    () => idsToChipItems(categories, filterOptions.industry),
    [categories]
  );
  const levelItems = useMemo(
    () => idsToChipItems(levels, filterOptions.level),
    [levels]
  );

  const counts: [string, number][] = [
    ['Work type', workTypes.length],
    ['Work place', workPlaces.length],
    ['Salary', salary.length],
    ['Industry', categories ? 1 : 0],
    ['Level', levels ? 1 : 0],
    ['Difficulty', difficultyEnabled ? difficulty.length : 0],
  ];

  const difficultyNumeric = Number(difficulty?.[0] || 1);

  const allChipItems: MergedChipItem[] = useMemo(() => {
    const items: MergedChipItem[] = [];

    workTypeItems.forEach((item) => {
      items.push({
        ...item,
        category: 'Work type',
        onRemove: (value) => {
          const next = workTypes.filter((v) => v !== value);
          setWorkTypes(next);
          setPage(1);
          syncUrl('workType', next);
        },
        onClearCategory: () => {
          setWorkTypes([]);
          setPage(1);
          syncUrl('workType', []);
        },
      });
    });

    workPlaceItems.forEach((item) => {
      items.push({
        ...item,
        category: 'Work place',
        onRemove: (value) => {
          const next = workPlaces.filter((v) => v !== value);
          setWorkPlaces(next);
          setPage(1);
          syncUrl('workPlace', next);
        },
        onClearCategory: () => {
          setWorkPlaces([]);
          setPage(1);
          syncUrl('workPlace', []);
        },
      });
    });

    salaryItems.forEach((item) => {
      items.push({
        ...item,
        category: 'Salary',
        onRemove: (value) => {
          setSalary([]);
          setPage(1);
          syncUrl('salary', '');
        },
        onClearCategory: () => {
          setSalary([]);
          setPage(1);
          syncUrl('salary', []);
        },
      });
    });

    industryItems.forEach((item) => {
      items.push({
        ...item,
        category: 'Industry',
        onRemove: (value) => {
          const next = categories.filter((v) => v !== value);
          setCategories(next);
          setPage(1);
          syncUrl('categories', next);
        },
        onClearCategory: () => {
          setCategories([]);
          setPage(1);
          syncUrl('categories', []);
        },
      });
    });

    levelItems.forEach((item) => {
      items.push({
        ...item,
        category: 'Level',
        onRemove: (value) => {
          const next = levels.filter((v) => v !== value);
          setLevels(next);
          setPage(1);
          syncUrl('levels', next);
        },
        onClearCategory: () => {
          setLevels([]);
          setPage(1);
          syncUrl('levels', []);
        },
      });
    });

    if (difficultyEnabled) {
      difficultyItems.forEach((item) => {
        items.push({
          ...item,
          category: 'Difficulty',
          onRemove: (value) => {
            const next = difficulty.filter((v) => v !== value);
            setDifficulty(next);
            setPage(1);
            syncUrl('level', next);
          },
          onClearCategory: () => {
            setDifficulty([]);
            setPage(1);
            syncUrl('level', []);
          },
        });
      });
    }

    return items;
  }, [
    workTypeItems,
    workPlaceItems,
    salaryItems,
    industryItems,
    difficultyItems,
    workTypes,
    workPlaces,
    salary,
    categories,
    difficulty,
    difficultyEnabled,
    setWorkTypes,
    setWorkPlaces,
    setSalary,
    setCategories,
    setDifficulty,
    setPage,
    syncUrl,
  ]);

  return (
    <>
      <div className="grid grid-cols-1 items-center gap-4 md:grid-cols-[auto,1fr]">
        <div className="flex items-center gap-2">
          <Checkbox
            variant="flat"
            size="sm"
            checked={difficultyEnabled}
            onChange={(e) => {
              const next = (e.target as HTMLInputElement).checked;
              setDifficultyEnabled(next);
              if (next) {
                if (difficulty.length) {
                  syncUrl('level', difficulty);
                } else {
                  syncUrl('level', ['1']);
                }
              } else {
                syncUrl('level', []);
              }
            }}
            label={
              <span className="text-[14px] text-slate-700">
                Show tryable roles now
              </span>
            }
          />
        </div>

        <div className="flex items-center justify-between gap-3">
          <div className="flex max-w-[450px] items-center gap-3">
            <span className="text-[14px] font-medium text-slate-500">
              Difficulty
            </span>
            <BrandSlider
              value={difficultyNumeric}
              onChange={(v) => {
                const arr = [String(v)];
                setDifficulty(arr);
                setPage(1);
                if (difficultyEnabled) syncUrl('level', arr);
              }}
              disabled={!difficultyEnabled}
            />
            <span className="text-[14px]" style={{ color: BRAND }}>
              {difficultyEnabled
                ? difficultyNumeric === 1
                  ? 'Beginner'
                  : difficultyNumeric === 2
                    ? 'Intermediate'
                    : 'Advanced'
                : 'Beginner'}
            </span>
          </div>
          {!isHomePage && (
            <div className="flex items-center gap-3">
              <Button
                // className="whitespace-nowrap rounded-md border border-[#c3c3c3] px-2 py-1 text-sm text-slate-700 hover:border-primary hover:text-primary"
                variant="outline"
                className="gap-1 rounded-xl border-primary text-primary hover:bg-primary hover:text-white"
                onClick={() => setOpenFilterModal(true)}
                size="sm"
              >
                <FilterIcon className="h-4 w-4" />
                <span>Filters</span>
              </Button>
            </div>
          )}
        </div>
      </div>

      {!isHomePage && (
        <>
          <hr className="my-4 border-t border-[#c3c3c3]" />
          <MergedChips
            items={allChipItems}
            onClear={trueClearAll}
            totalSelected={totalSelected}
          />
        </>
      )}

      {openFilterModal && (
        <JobFilterModal
          open={openFilterModal}
          onClose={() => setOpenFilterModal(false)}
          minSalaryRef={minSalaryRef}
          maxSalaryRef={maxSalaryRef}
          industryRef={industryRef}
          handleFilterChange={handleFilterChange}
          syncUrl={syncUrl}
          onClear={trueClearAll}
          levelRef={levelRef}
        />
      )}
    </>
  );
}

export default function JobFilterV2({
  isHomePage,
  searchRef,
  locationRef,
}: IProps) {
  return (
    <Suspense fallback={<></>}>
      <JobFilterContent
        isHomePage={isHomePage}
        searchRef={searchRef}
        locationRef={locationRef}
      />
    </Suspense>
  );
}
