'use client';

import SearchIcon from '@/views/icons/search';
import { Input, InputProps, Select, type SelectProps } from 'rizzui';
import { SelectOption } from '@/api-requests/types';
import { JobStatus } from '@/api-requests/job';
import { employementOptions } from '../job-creation/options';
import LocationIcon from '../icons/location';
import { difficultyOptions } from '../banner/job-banner/job-filter';

const matchOptions: SelectOption[] = [
  { label: 'Published', value: JobStatus.PUBLISHED },
  { label: 'Unpublished', value: JobStatus.UNPUBLISHED },
  { label: 'Draft', value: JobStatus.DRAFT },
  { label: 'Close', value: JobStatus.CLOSED },
];

const sortOptions: SelectOption[] = [
  { label: 'Candidates (ascending)', value: 'candidate_ascending' },
  { label: 'Candidates (descending)', value: 'candidate_descending' },
  { label: 'Created (newest)', value: 'created_newest' },
  { label: 'Created (oldest)', value: 'created oldest' },
  { label: 'Last apply (recent)', value: 'last_apply_recent' },
  { label: 'Last apply (older)', value: 'last_apply_older' },
];

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  searchLocationProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  jobTypeProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  simulationProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  sortByProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  resetProps: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

export default function AllJobsFilter({
  searchProps,
  statusProps,
  searchLocationProps,
  jobTypeProps,
  simulationProps,
  sortByProps,
  resetProps,
}: IProps) {
  return (
    <div className="flex flex-col gap-4 2xl:flex-row 2xl:items-center 2xl:justify-between">
      <div className="flex flex-col gap-4 sm:flex-row">
        <Input
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search job title"
          className="w-full min-w-[220px]"
          inputClassName="rounded-lg"
          {...searchProps}
        />
        <Input
          variant="flat"
          prefix={<LocationIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search location"
          className="w-full min-w-[220px]"
          inputClassName="rounded-lg"
          {...searchLocationProps}
        />
      </div>

      <div className="flex flex-col gap-3 xl:flex-row xl:items-center 2xl:justify-end">
        <div className="flex flex-wrap items-center gap-3">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <div className="flex flex-wrap gap-3">
            <Select
              clearable
              options={matchOptions}
              placeholder="Status"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg"
              {...statusProps}
            />
            <Select
              clearable
              options={employementOptions}
              placeholder="Job type"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg"
              {...jobTypeProps}
            />
            <Select
              clearable
              options={difficultyOptions}
              placeholder="Simulation"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg"
              {...simulationProps}
            />
            <Select
              clearable
              options={sortOptions}
              placeholder="Sort by"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg"
              {...sortByProps}
            />
          </div>

          <button
            className="whitespace-nowrap text-sm text-slate-600 underline transition-colors hover:text-slate-900"
            {...resetProps}
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );
}
