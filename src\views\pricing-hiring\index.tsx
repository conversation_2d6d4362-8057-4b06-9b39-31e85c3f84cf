'use client';

import Image from 'next/image';
import { Button } from 'rizzui';

export default function PricingHiring() {
  return (
    <div className="bg-[#0D1321] py-10">
      <div className="mx-auto flex max-w-[1200px] flex-col-reverse items-center justify-between gap-12 px-4 lg:flex-row xl:px-0">
        {/* Left content */}
        <div className="max-w-xl text-center lg:text-left">
          <h2 className="mb-4 text-2xl font-bold text-white md:text-3xl">
            Ready To Transform Your Hiring?
          </h2>
          <p className="mb-8 text-gray-300">
            Join hundreds of forward-thinking employers who are hiring based on
            skills, not just CVs
          </p>
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row lg:justify-start">
            <Button className="bg-primary text-white">
              Post Your First Job
            </Button>
            <Button className="text-primary bg-white hover:text-white">Schedule Demo</Button>
          </div>
        </div>

        {/* Right image */}
        <div className="w-full max-w-md">
          <Image
            src="/pricing/interview.png"
            alt="Transform Hiring Illustration"
            className="h-auto w-full"
            width={500}
            height={500}
            loader={({ src }) => src}
          />
        </div>
      </div>
    </div>
  );
}
