import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { AT_COOKIE, isProd, maxAgeDays, RT_COOKIE } from '../_utils';
import axiosInstance from '@/utils/http-client';
import { API_DOMAINS, API_ENDPONTS } from '@/config/endpoint';

// function decodeExp(token?: string | null): number | null {
//   if (!token) return null;
//   try {
//     const payload = JSON.parse(
//       Buffer.from(token.split('.')[1], 'base64').toString('utf8')
//     );
//     return typeof payload?.exp === 'number' ? payload.exp : null;
//   } catch {
//     return null;
//   }
// }

async function handleInternalRefresh(rt: string) {
  try {
    const { data } = await axiosInstance.post(
      API_ENDPONTS.REFRESH_TOKEN,
      {},
      { headers: { Authorization: `Bearer ${rt}` } }
    );

    return data;
  } catch (error) {
    return { shouldRedirect: true, message: 'Not authorization' };
  }
}

export async function GET() {
  const jar = await cookies();
  const at = jar.get(AT_COOKIE)?.value;
  const rt = jar.get(RT_COOKIE)?.value as string;

  // // chưa có AT -> thử refresh
  // if (!at) {
  //   if (!rt) {
  //     return NextResponse.json(
  //       { ok: false, message: 'Not authorization' },
  //       { status: 500 }
  //     );
  //   }
  //   try {
  //     const refreshed = await handleInternalRefresh(rt);

  //     if (!refreshed?.user) {
  //       return NextResponse.json(
  //         { ok: false, message: 'Not authorization' },
  //         { status: 500 }
  //       );
  //     }

  //     const res = NextResponse.json({ user: refreshed.user });

  //     res.cookies.set(AT_COOKIE, refreshed.accessToken, {
  //       httpOnly: false,
  //       secure: isProd,
  //       sameSite: 'lax',
  //       path: '/',
  //       maxAge: maxAgeDays(1),
  //     });

  //     res.cookies.set(RT_COOKIE, refreshed.refreshToken, {
  //       httpOnly: true,
  //       secure: isProd,
  //       sameSite: 'lax',
  //       path: '/',
  //       maxAge: maxAgeDays(7),
  //     });

  //     return res;
  //   } catch {
  //     return NextResponse.json({ authenticated: false, user: null });
  //   }
  // }

  // // có AT: nếu còn <= 5 phút thì refresh
  // const exp = decodeExp(at);
  // if (exp && exp * 1000 - Date.now() <= 5 * 60 * 1000) {
  //   if (!rt) {
  //     return NextResponse.json(
  //       { ok: false, message: 'Not authorization' },
  //       { status: 500 }
  //     );
  //   }

  //   const refreshed = await handleInternalRefresh(rt);

  //   if (!refreshed?.user) {
  //     return NextResponse.json(
  //       { ok: false, message: 'Not authorization' },
  //       { status: 500 }
  //     );
  //   }

  //   const res = NextResponse.json({ user: refreshed.user });

  //   res.cookies.set(AT_COOKIE, refreshed.accessToken, {
  //     httpOnly: false,
  //     secure: isProd,
  //     sameSite: 'lax',
  //     path: '/',
  //     maxAge: maxAgeDays(1),
  //   });

  //   res.cookies.set(RT_COOKIE, refreshed.refreshToken, {
  //     httpOnly: true,
  //     secure: isProd,
  //     sameSite: 'lax',
  //     path: '/',
  //     maxAge: maxAgeDays(7),
  //   });

  //   return res;
  // }

  try {
    const { data } = await axiosInstance.get(API_ENDPONTS.USER_PROFILE, {
      headers: { Authorization: `Bearer ${at}` },
    });

    return NextResponse.json({
      user: data,
    });
  } catch {
    const refreshed = await handleInternalRefresh(rt);
    if (refreshed?.user) {
      return NextResponse.json({ user: refreshed.user });
    }

    return NextResponse.json({
      shouldRedirect: !!at || !!rt ? true : false,
      message: 'Not authorization',
    });

    // return NextResponse.json(
    //   { ok: false, message: 'Not authorization' },
    //   { status: 401 }
    // );
  }
}
