'use client';

import { useEffect, useMemo, useState } from 'react';
import {
  Modal,
  Input,
  Select,
  ActionIcon,
  Title,
  Button,
  Text,
  Avatar,
  MultiSelect,
  MultiSelectOption,
  Checkbox,
} from 'rizzui';
import countriesData from '@/data/countries.json';
import FieldLabel from '@/views/job-creation/field-label';
import CloseIcon from '@/views/icons/close';
import { Controller, useForm } from 'react-hook-form';
import ImageUploader from '@/views/image-uploader';
import UploadIcon from '@/views/icons/upload';
import Image from 'next/image';
import DeleteIcon from '@/views/icons/delete';
import {
  UpdateUserProfileParams,
  UserProfile,
  useUpdateUserProfile,
} from '@/api-requests/user-profile';
import toast from 'react-hot-toast';
import { Role, updateUserAtom, userAtom, UserInfo } from '@/store/user-atom';
import { useSetAtom } from 'jotai';
import {
  categoryOptions,
  employementOptions,
  levelOptions,
  workplaceOptions,
} from '@/views/job-creation/options';
import cn from '@/utils/class-names';
import RangeSlider from '@/views/range-slider';
import ImageUploaderWithCrop from '@/shared/image-uploader-with-crop';

type FormValues = {
  firstName?: string;
  lastName?: string;
  gender?: {
    label: string;
    value: string;
  } | null;
  address?: string;
  city?: string;
  region?: string;
  country: {
    label: string;
    value: string;
  } | null;
  file?: File | null;
  industries?: string[];
  levels?: string[];
  workTypes?: string[];
  workPlaces?: string[];
  skills?: string[];
  expectedSalary?: {
    min?: number;
    max?: number;
  };
  experience?: {
    min?: number;
    max?: number;
  };
};

interface IProps {
  open: boolean;
  onClose: () => void;
  refetch?: () => void;
  profile: UserProfile | null;
  user: UserInfo | null;
}

interface Option {
  label: string;
  value: string;
}

const genderOptions: Option[] = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
];

function renderDisplayValue(
  selectedItems: string[],
  options: MultiSelectOption[],
  title: string
) {
  const filteredItems = options.filter((option) =>
    selectedItems.includes(option.value)
  );

  return (
    <div className={cn('flex w-full flex-wrap items-center text-start')}>
      <div>{title}</div>
      <span className="border-muted ms-2 border-s ps-2">
        {filteredItems.length} Selected
      </span>
    </div>
  );
}

function renderOptionDisplayValue(
  option: MultiSelectOption,
  selected: boolean
) {
  return (
    <div
      className="flex w-full cursor-pointer items-center justify-between gap-2 py-1"
      onClick={(e) => {
        e.currentTarget.dispatchEvent(
          new MouseEvent('mousedown', { bubbles: true })
        );
      }}
    >
      <div className="flex items-center gap-1">
        <span className="text-sm">{option.label}</span>
        {option.description && (
          <span className="text-xs text-gray-500">({option.description})</span>
        )}
      </div>
      <Checkbox
        variant="flat"
        size="sm"
        checked={selected}
        onClick={(e) => {
          e.stopPropagation();
          e.currentTarget.dispatchEvent(
            new MouseEvent('mousedown', { bubbles: true })
          );
        }}
        readOnly
      />
    </div>
  );
}

export default function ProfileDetailModal({
  open,
  onClose,
  refetch,
  profile,
  user,
}: IProps) {
  const updateUser = useSetAtom(updateUserAtom);
  const [error, setError] = useState<string>('');
  const [skill, setSkill] = useState<string>('');

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  const { mutateAsync } = useUpdateUserProfile();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid, isSubmitting },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      firstName: profile?.firstName || user?.firstName || '',
      lastName: profile?.lastName || user?.lastName || '',
      gender: genderOptions.find((g) => g.value === profile?.gender) || null,
      address: profile?.address || '',
      city: profile?.city || '',
      region: profile?.region || '',
      country: countryOptions.find((c) => c.label === profile?.country) || null,
      file: null,
      skills: profile?.skills || [],
      industries: profile?.industries || [],
      levels: profile?.levels || [],
      workTypes: profile?.workTypes || [],
      workPlaces: profile?.workPlaces || [],
      expectedSalary: {
        min: profile?.expectedSalary?.min || 0,
        max: profile?.expectedSalary?.max || 0,
      },
      experience: {
        min: profile?.experience?.min || 0,
        max: profile?.experience?.max || 0,
      },
    },
  });

  const file = watch('file');
  const skills = watch('skills') || [];
  const expectedSalary = watch('expectedSalary');
  const experience = watch('experience');

  const preview = useMemo(
    () => (file ? URL.createObjectURL(file) : user?.avatar || ''),
    [file]
  );
  useEffect(() => {
    return () => {
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [preview]);

  const onSubmit = async (data: FormValues) => {
    if (!isValid) return;

    const resp = await mutateAsync({
      ...data,
      userId: user?.id,
      country: data.country?.label,
      gender: data.gender?.value,
      expectedSalary: {
        min: parseInt(data.expectedSalary?.min as any, 10) || 0,
        max: parseInt(data.expectedSalary?.max as any, 10) || 0,
        currency: 'USD',
      },
      experience: {
        min: parseInt(data.experience?.min as any, 10) || 0,
        max: parseInt(data.experience?.max as any, 10) || 0,
      },
    } as UpdateUserProfileParams);

    if (resp) {
      setError('');
      onClose();
      refetch?.();
      updateUser({
        firstName: resp.firstName,
        lastName: resp.lastName,
        avatar: resp.avatar,
      });
    } else {
      toast.error('Failed to update profile');
    }
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div
        className="scrollbar-hide max-h-[80vh] w-full overflow-y-auto rounded-[20px] p-6 md:w-[640px]"
        style={{
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
        }}
      >
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">Edit Profile</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div className="text-lg font-bold">Personal Information</div>

          <div>
            <FieldLabel title="Avatar" />
            <ImageUploaderWithCrop
              onChange={(f: File | null) => {
                setError('');
                setValue('file', f);
              }}
              onError={(msg) => setError(msg)}
            >
              {({ getRootProps, dragActive }) => (
                <div
                  {...getRootProps()}
                  className={[
                    'relative flex h-[100px] w-[100px] cursor-pointer items-center justify-center rounded-full border bg-[#c3c3c3]',
                    dragActive
                      ? 'border-gray-400'
                      : 'border-gray-200 hover:border-gray-300',
                  ].join(' ')}
                  role="button"
                  aria-label="Upload image"
                >
                  {!preview ? (
                    <div className="flex flex-col items-center text-gray-400">
                      <UploadIcon className="h-10 w-10" />
                    </div>
                  ) : (
                    <>
                      <Avatar
                        src={preview}
                        name={user?.firstName + ' ' + user?.lastName}
                        customSize={100}
                        className="!bg-transparent"
                      />

                      <ActionIcon
                        variant="outline"
                        size="sm"
                        rounded="full"
                        className="group absolute bottom-1 left-1/2 h-6 w-6 -translate-x-1/2 bg-gray-200 hover:bg-primary hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          setValue('file', null);
                        }}
                      >
                        <DeleteIcon className="h-3 w-3 group-hover:text-white" />
                      </ActionIcon>
                    </>
                  )}
                </div>
              )}
            </ImageUploaderWithCrop>
            {error && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {error}
              </Text>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="First name" />
              <Controller
                name="firstName"
                control={control}
                rules={{
                  required: 'Please enter first name',
                  maxLength: {
                    value: 24,
                    message: 'First name cannot exceed 24 characters',
                  },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your full name"
                    className="w-full"
                  />
                )}
              />
              {errors.firstName && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.firstName.message}
                </Text>
              )}
            </div>
            <div>
              <FieldLabel title="Last name" />
              <Controller
                name="lastName"
                control={control}
                rules={{
                  required: 'Please enter last name',
                  maxLength: {
                    value: 24,
                    message: 'First name cannot exceed 24 characters',
                  },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your last name"
                    className="w-full"
                  />
                )}
              />
              {errors.lastName && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.lastName.message}
                </Text>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="Gender" />
              <Controller
                name="gender"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Select
                    {...field}
                    clearable
                    onClear={() => field.onChange(null)}
                    options={genderOptions}
                    placeholder="Select gender"
                    className="w-full"
                  />
                )}
              />
            </div>

            <div>
              <FieldLabel title="Address" />
              <Controller
                name="address"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your address"
                    className="w-full"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="City" />
              <Controller
                name="city"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your city"
                    className="w-full"
                  />
                )}
              />
            </div>

            <div>
              <FieldLabel title="Region" />
              <Controller
                name="region"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your region"
                    className="w-full"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="Country" />
              <Controller
                name="country"
                control={control}
                rules={{
                  required: 'Please select your country',
                }}
                render={({ field }) => (
                  <Select
                    {...field}
                    clearable
                    onClear={() => field.onChange(null)}
                    options={countryOptions}
                    placeholder="Select your country"
                    className="w-full"
                    searchable={true}
                  />
                )}
              />
              {errors.country && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.country.message}
                </Text>
              )}
            </div>
          </div>

          {user?.role === Role.USER && (
            <>
              <div className="text-lg font-bold">General Information</div>

              <div>
                <FieldLabel
                  title="Add skills"
                  content="Press Enter to add skill"
                />
                <Controller
                  name="skills"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      value={skill}
                      onChange={(e) => setSkill(e.target.value)}
                      variant="flat"
                      placeholder="Press Enter to add skill"
                      className="w-full"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && skill.trim()) {
                          e.preventDefault();
                          field.onChange([
                            ...(field.value as string[]),
                            skill.trim(),
                          ]);
                          setSkill('');
                        }
                      }}
                    />
                  )}
                />
                {skills.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-3">
                    {skills.map((s, index) => (
                      <span
                        key={index}
                        className="inline-flex h-8 items-center gap-1 rounded-full border border-[#c3c3c3] bg-white px-3 text-sm"
                      >
                        <button
                          type="button"
                          onClick={() =>
                            setValue(
                              'skills',
                              skills.filter((_, i) => i !== index)
                            )
                          }
                          className="rounded-full border border-[#222222] text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                        >
                          <CloseIcon className="h-3 w-3" />
                        </button>
                        {s}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
                <div>
                  <FieldLabel title="Industry" />
                  <Controller
                    name="industries"
                    control={control}
                    rules={{
                      required: false,
                    }}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        clearable
                        onClear={() => field.onChange([])}
                        options={categoryOptions}
                        placeholder="Select industry"
                        className="w-full"
                        getOptionDisplayValue={renderOptionDisplayValue}
                        displayValue={(selectedItems, options) =>
                          renderDisplayValue(selectedItems, options, 'Category')
                        }
                      />
                    )}
                  />
                </div>
                <div>
                  <FieldLabel title="Level" />
                  <Controller
                    name="levels"
                    control={control}
                    rules={{
                      required: false,
                    }}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        clearable
                        onClear={() => field.onChange([])}
                        options={levelOptions}
                        placeholder="Select level"
                        className="w-full"
                        getOptionDisplayValue={renderOptionDisplayValue}
                        displayValue={(selectedItems, options) =>
                          renderDisplayValue(selectedItems, options, 'Level')
                        }
                      />
                    )}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
                <div>
                  <FieldLabel title="Work type" />
                  <Controller
                    name="workTypes"
                    control={control}
                    rules={{
                      required: false,
                    }}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        clearable
                        onClear={() => field.onChange([])}
                        options={employementOptions}
                        placeholder="Select work type"
                        className="w-full"
                        getOptionDisplayValue={renderOptionDisplayValue}
                        displayValue={(selectedItems, options) =>
                          renderDisplayValue(
                            selectedItems,
                            options,
                            'Work type'
                          )
                        }
                      />
                    )}
                  />
                </div>
                <div>
                  <FieldLabel title="Work place" />
                  <Controller
                    name="workPlaces"
                    control={control}
                    rules={{
                      required: false,
                    }}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        clearable
                        onClear={() => field.onChange([])}
                        options={workplaceOptions}
                        placeholder="Select Work place"
                        className="w-full"
                        getOptionDisplayValue={renderOptionDisplayValue}
                        displayValue={(selectedItems, options) =>
                          renderDisplayValue(
                            selectedItems,
                            options,
                            'Work place'
                          )
                        }
                      />
                    )}
                  />
                </div>
              </div>

              <div>
                <FieldLabel title="Expected salary (USD)" />
                <div className="flex justify-between text-sm text-gray-400">
                  <span>0</span>
                  <span>100,000</span>
                </div>
                <Controller
                  name="expectedSalary"
                  control={control}
                  render={({ field }) => (
                    <RangeSlider
                      range
                      step={100}
                      min={0}
                      max={100000}
                      onChange={(val) => {
                        const [min, max] = val as [number, number];
                        field.onChange({
                          ...field.value,
                          min,
                          max,
                        });
                      }}
                      value={[field.value?.min || 0, field.value?.max || 0]}
                    />
                  )}
                />
                <div className="flex justify-between">
                  <span>
                    {(expectedSalary?.min as number).toLocaleString()}
                  </span>
                  <span>
                    {(expectedSalary?.max as number).toLocaleString()}
                  </span>
                </div>
              </div>

              <div>
                <FieldLabel title="Eperience (year)" />
                <div className="flex justify-between text-sm text-gray-400">
                  <span>0</span>
                  <span>30</span>
                </div>
                <Controller
                  name="experience"
                  control={control}
                  render={({ field }) => (
                    <RangeSlider
                      range
                      step={1}
                      min={0}
                      max={30}
                      onChange={(val) => {
                        const [min, max] = val as [number, number];
                        field.onChange({
                          ...field.value,
                          min,
                          max,
                        });
                      }}
                      value={[field.value?.min || 0, field.value?.max || 0]}
                    />
                  )}
                />
                <div className="flex justify-between">
                  <span>{(experience?.min as number).toLocaleString()}</span>
                  <span>{(experience?.max as number).toLocaleString()}</span>
                </div>
              </div>
            </>
          )}
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <Button variant="outline" className="border-primary">
            Cancel
          </Button>
          <Button
            className="bg-primary text-white"
            onClick={handleSubmit(onSubmit)}
            disabled={!user || isSubmitting || user.id !== profile?.userId}
            isLoading={isSubmitting}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
}
