'use client';

import FieldLabel from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import { Input, Button, Text } from 'rizzui';
import { ActionType } from './sign-in-modal';
import { useAuthActions } from '@/hooks/use-auth-actions';
import toast from 'react-hot-toast';
import { UserInfo } from '@/store/user-atom';

type FormValues = {
  email: string;
};

interface IProps {
  onSetAction: (action: ActionType) => void;
  onVerifyType: (action: ActionType) => void;
  onLoginSuccess?: (respUser: UserInfo) => void;
}

export default function EmailForm({
  onSetAction,
  onVerifyType,
  onLoginSuccess,
}: IProps) {
  const { loginRequestCode } = useAuthActions();
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const onSubmit = async (data: FormValues) => {
    const resp = await loginRequestCode(data.email);
    if (resp?.status === true) {
      onSetAction(ActionType.VERIFY_CODE);
      onVerifyType(ActionType.SIGN_IN);
      onLoginSuccess?.(resp?.user);
    } else {
      toast.error(resp?.message || 'Something went wrong');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <div className="font-bold">Welcome to Industry Connect Career</div>
        <div className="text-sm text-[#484848]">
          Welcome back, please enter your account information to continue.
        </div>
      </div>

      <div>
        <FieldLabel title="Email" />
        <Controller
          name="email"
          control={control}
          rules={{
            required: 'Please enter email',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i,
              message: 'Invalid email',
            },
          }}
          render={({ field }) => (
            <Input
              {...field}
              type="email"
              variant="flat"
              placeholder="<EMAIL>"
              className="w-full"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (isValid) {
                    e.preventDefault();
                    handleSubmit(onSubmit)();
                  }
                }
              }}
            />
          )}
        />
        {errors.email && (
          <Text as="p" className="mt-0.5 text-xs text-red-600">
            {errors.email.message}
          </Text>
        )}
      </div>

      <div className="space-y-3">
        <Button
          type="submit"
          className="w-full bg-primary text-white"
          disabled={!isValid || isSubmitting}
          onClick={handleSubmit(onSubmit)}
          isLoading={isSubmitting}
        >
          Continue
        </Button>

        <Button
          type="button"
          variant="outline"
          className="w-full border-primary text-primary"
          onClick={() => {
            onSetAction(ActionType.CONTINUE_WITH_PASSWORD);
          }}
        >
          Continue with password
        </Button>

        <div className="flex justify-between text-sm">
          <div>
            <span className="text-[#484848]">Don’t have an account?</span>{' '}
            <button
              className="italic text-primary underline underline-offset-2 hover:opacity-80"
              onClick={(e) => {
                e.stopPropagation();
                onSetAction(ActionType.SIGN_UP);
              }}
            >
              Sign Up
            </button>
          </div>
          <button
            className="italic text-primary underline underline-offset-2 hover:opacity-80"
            onClick={() => onSetAction(ActionType.RESET_PASSWORD)}
          >
            Forgot password?
          </button>
        </div>
      </div>
    </div>
  );
}
