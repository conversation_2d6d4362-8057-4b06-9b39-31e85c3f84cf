'use client';

import FieldLabel from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import {
  Input,
  Button,
  Text,
  Textarea,
  Select,
  RadioGroup,
  Radio,
} from 'rizzui';
import { ActionType, OrganizationType } from './sign-in-modal';
import ArrowLeftIcon from '@/views/icons/arrow-left';
import { useEffect, useMemo, useState } from 'react';
import countriesData from '@/data/countries.json';
import ImageUploader from '@/views/image-uploader';
import UploadIcon from '@/views/icons/upload';
import Image from 'next/image';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { Role } from '@/api-requests/user/types';
import toast from 'react-hot-toast';
import { useGetOrganizations } from '@/api-requests/organization/get-orgs';

type FormValues = {
  code: string[];
  firstName?: string;
  lastName?: string;
  email: string;
  password: string;
  role: string;
  file?: File | null;
  organizationName?: string;
  description?: string;
  address?: string;
  city?: string;
  region?: string;
  country?: {
    label: string;
    value: string;
  };
  orgType: {
    type: string;
    role: string;
  };
  organization: {
    label: string;
    value: string;
  } | null;
};

const options = [
  {
    id: 'company',
    type: 'company',
    role: 'employer',
    title: 'Employer',
    description: 'I want to hire or manage job postings.',
    image: '/org/employer.png',
  },
  {
    id: 'education',
    type: 'education',
    role: 'education',
    title: 'Education',
    description: 'I represent an educational institution or training center.',
    image: '/org/education.png',
  },
  {
    id: 'community',
    type: 'community',
    role: 'community',
    title: 'Community',
    description: 'I manage or represent a community, group, organization...',
    image: '/org/community.png',
  },
];

interface IProps {
  onSetAction: (action: string) => void;
  onVerifyType: (action: string) => void;
  role: string;
}

export default function CompanyForm({ onSetAction, onVerifyType }: IProps) {
  const { signupRequestCode } = useAuthActions();

  const [selected, setSelected] = useState('employer');
  const [radioValue, setRadioValue] = useState('exist');
  const [error, setError] = useState<string>('');

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const file = watch('file');
  const orgType = watch('orgType');

  const { data } = useGetOrganizations({
    skip: 0,
    limit: 50,
    type: orgType?.type,
  });

  const orgOptions = useMemo(
    () =>
      (data || []).map((org) => ({
        label: org.name,
        value: org._id,
      })),
    [data]
  );

  const preview = useMemo(
    () => (file ? URL.createObjectURL(file) : ''),
    [file]
  );
  useEffect(() => {
    return () => {
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [preview]);

  const onSubmit = async (data: FormValues) => {
    const resp = await signupRequestCode({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      role: data.orgType.role,
      file: data.file,
      organizationName: data.organizationName,
      description: data.description,
      address: data.address,
      city: data.city,
      region: data.region,
      country: data?.country?.label,
      type: data.orgType.type,
      orgId: data.organization?.value,
    });
    if (resp?.status === true) {
      onVerifyType(ActionType.SIGN_UP);
      onSetAction(ActionType.VERIFY_CODE);
    } else {
      toast.error(resp?.message || 'Failed to send verification code');
    }
  };

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  return (
    <div className="space-y-8">
      <div>
        <div className="flex items-center gap-2">
          <button onClick={() => onSetAction(ActionType.SIGN_UP)}>
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div className="font-bold">Create Your Employer Profile</div>
        </div>
        <div className="text-sm text-[#484848]">
          You&apos;re just one step away. Tell us a bit about yourself and your
          business so we can verify your account. Don’t worry your information
          stays private.
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <div className="inline-block">
            <div
              className="bg-primary px-2 py-1 text-sm font-medium text-white"
              style={{
                clipPath:
                  'polygon(0 0, calc(100% - 16px) 0, 100% 50%, calc(100% - 16px) 100%, 0 100%)',
                borderRadius: '6px',
                display: 'inline-block',
                borderRight: '2px solid rgba(255,255,255,0.9)',
                borderLeft: '1px solid rgba(255,255,255,0.1)',
              }}
            >
              Step 1:
            </div>
          </div>
          <div>Select Your Category</div>
        </div>

        <div className="flex flex-wrap gap-4 sm:flex-nowrap">
          {options.map((option) => {
            return (
              <Controller
                key={option.id}
                name="orgType"
                control={control}
                rules={{
                  required: 'Please upload your business logo',
                }}
                render={({ field }) => {
                  const isSelected = field.value.type === option.type;
                  return (
                    <div
                      onClick={() =>
                        field.onChange({
                          type: option.type,
                          role: option.role,
                        })
                      }
                      className={`relative w-full cursor-pointer rounded-xl border border-[#c3c3c3] p-4 transition-all duration-200 sm:w-60 ${
                        isSelected
                          ? 'border-primary shadow-[0_2px_0px_0px_rgba(0,0,0,1)]'
                          : 'border-[#c3c3c3]'
                      }`}
                    >
                      <div
                        className={`absolute left-3 top-3 flex h-5 w-5 items-center justify-center rounded-full border ${
                          isSelected ? 'border-primary' : 'border-[#c3c3c3]'
                        }`}
                      >
                        {isSelected && (
                          <div className="h-3 w-3 rounded-full bg-primary" />
                        )}
                      </div>

                      <div className="flex justify-end">
                        <Image
                          src={option.image}
                          alt={option.title}
                          width={48}
                          height={48}
                        />
                      </div>

                      <div className="mb-1 ml-1 text-base font-semibold text-primary">
                        {option.title}
                      </div>
                      <div className="ml-1 text-sm text-gray-500">
                        {option.description}
                      </div>
                    </div>
                  );
                }}
              />
            );
          })}
        </div>

        <div className="flex items-center gap-2 pt-3">
          <div className="inline-block">
            <div
              className="bg-primary px-2 py-1 text-sm font-medium text-white"
              style={{
                clipPath:
                  'polygon(0 0, calc(100% - 16px) 0, 100% 50%, calc(100% - 16px) 100%, 0 100%)',
                borderRadius: '6px',
                display: 'inline-block',
                borderRight: '2px solid rgba(255,255,255,0.9)',
                borderLeft: '1px solid rgba(255,255,255,0.1)',
              }}
            >
              Step 2:
            </div>
          </div>
          <div>Tell us about your {selected}</div>
        </div>

        <RadioGroup
          value={radioValue}
          setValue={(val) => {
            setRadioValue(val);
          }}
          className="flex gap-3"
        >
          <Radio
            label="Exist employer"
            value="exist"
            size="sm"
            className="[&_.rizzui-radio-label]:!text-base"
          />
          <Radio
            label="Create new employer"
            value="new"
            size="sm"
            className="[&_.rizzui-radio-label]:!text-base"
          />
        </RadioGroup>

        {radioValue === 'new' ? (
          <>
            <div>
              <FieldLabel title="Business Logo" />
              <Controller
                name="file"
                control={control}
                rules={{
                  required: 'Please upload your business logo',
                }}
                render={({ field }) => (
                  <ImageUploader
                    onChange={(f: File | null) => {
                      setError('');
                      field.onChange(f);
                    }}
                    onError={(msg) => setError(msg)}
                  >
                    {({ getRootProps, dragActive }) => (
                      <div
                        {...getRootProps()}
                        className={[
                          'relative flex h-[160px] w-[160px] cursor-pointer items-center justify-center rounded-full border border-[#c3c3c3] bg-white transition',
                          dragActive
                            ? 'border-gray-400'
                            : 'border-gray-200 hover:border-[#c3c3c3]',
                        ].join(' ')}
                        role="button"
                        aria-label="Upload image"
                      >
                        {!preview ? (
                          <div className="flex flex-col items-center text-gray-400">
                            <UploadIcon className="h-10 w-10" />
                          </div>
                        ) : (
                          <Image
                            src={preview}
                            alt="Preview"
                            width={160}
                            height={160}
                            className="h-full w-full rounded-full object-cover"
                            loader={({ src }) => src}
                          />
                        )}
                      </div>
                    )}
                  </ImageUploader>
                )}
              />
              {errors.file && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.file.message}
                </Text>
              )}
            </div>

            <div>
              <FieldLabel title="Business name" />
              <Controller
                name="organizationName"
                control={control}
                rules={{
                  required: 'Please enter business name',
                  maxLength: {
                    value: 64,
                    message: 'Business name cannot exceed 64 characters',
                  },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your business name"
                    className="w-full"
                  />
                )}
              />
              {errors.organizationName && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.organizationName.message}
                </Text>
              )}
            </div>

            <div>
              <FieldLabel title="Description" />
              <Controller
                name="description"
                control={control}
                rules={{
                  required: false,
                  maxLength: {
                    value: 500,
                    message: 'Description cannot exceed 500 characters',
                  },
                }}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    variant="flat"
                    placeholder="Enter your description"
                    className="w-full"
                  />
                )}
              />
              {errors.description && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.description.message}
                </Text>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <FieldLabel title="Address" />
                <Controller
                  name="address"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your address"
                      className="w-full"
                    />
                  )}
                />
                {errors.address && (
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.address.message}
                  </Text>
                )}
              </div>

              <div>
                <FieldLabel title="City" />
                <Controller
                  name="city"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your city"
                      className="w-full"
                    />
                  )}
                />
                {errors.city && (
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.city.message}
                  </Text>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <FieldLabel title="Region" />
                <Controller
                  name="region"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your region"
                      className="w-full"
                    />
                  )}
                />
                {errors.region && (
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.region.message}
                  </Text>
                )}
              </div>

              <div>
                <FieldLabel title="Country" />
                <Controller
                  name="country"
                  control={control}
                  rules={{
                    required: 'Please select your country',
                  }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      clearable
                      onClear={() => field.onChange(null)}
                      options={countryOptions}
                      placeholder="Select your country"
                      className="w-full"
                      searchable={true}
                    />
                  )}
                />
                {errors.country && (
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.country.message}
                  </Text>
                )}
              </div>
            </div>
          </>
        ) : (
          <div>
            <FieldLabel title="Organization" />
            <Controller
              name="organization"
              control={control}
              rules={{
                required: 'Please select your organization',
              }}
              render={({ field }) => (
                <Select
                  {...field}
                  clearable
                  onClear={() => field.onChange(null)}
                  options={orgOptions}
                  placeholder="Select your organization"
                  className="w-full"
                  searchable={true}
                />
              )}
            />
            {errors.organization && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.organization.message}
              </Text>
            )}
          </div>
        )}
      </div>

      <Button
        type="submit"
        className="w-full bg-primary text-white"
        // disabled={!isValid || isSubmitting}
        onClick={handleSubmit(onSubmit)}
        isLoading={isSubmitting}
      >
        Create Account
      </Button>
    </div>
  );
}
