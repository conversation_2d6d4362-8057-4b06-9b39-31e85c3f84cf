'use client';

import { Job } from '@/api-requests/job';
import ApplyCVButton from '@/components/ApplyCVButton';
import StartSimulationButton from '@/components/StartSimulationButton';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import ClockIcon from '@/views/icons/clock';
import MoneyIcon from '@/views/icons/money';
import TechnologyIcon from '@/views/icons/technology';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import { useAtom } from 'jotai';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from 'rizzui/button';

interface JobCardProps {
  job: Job;
  onApplyCVSuccess: () => void;
  onErrorJobApplied: () => void;
}

const removeAllHTML = (str: string) => {
  try {
    const doc = new DOMParser().parseFromString(str, 'text/html');
    return doc.body.textContent || '';
  } catch (error) {
    return str;
  }
};

export default function OrgHomepageJobCard({
  job,
  onApplyCVSuccess,
  onErrorJobApplied,
}: JobCardProps) {
  const router = useRouter();

  const [user] = useAtom(userAtom);

  const jobType = getJobTypeString(job);

  const handleGotoDetail = () => {
    router.push(`/org/${job.orgId}/jobs/${job.jobId}`);
  };

  return (
    <div
      className={cn(
        'flex h-full flex-col justify-between rounded-[16px] border border-[#c3c3c3] bg-white p-4'
      )}
    >
      <div>
        {/* Title + Badge */}
        <div className="flex items-start justify-between gap-1">
          <div>
            <h2
              className="text-md line-clamp-2 flex-1 cursor-pointer font-semibold text-gray-800 hover:underline"
              onClick={handleGotoDetail}
            >
              {job?.title}
            </h2>
            {['simulation', 'all'].includes(job?.applyMode as string) && (
              <div className="mt-1 flex flex-row flex-wrap gap-1">
                {job.simulation?.id && (
                  <span className="whitespace-nowrap rounded-full border border-primary bg-[#CCFFE7] px-3 py-0.5 text-[10px] font-medium text-primary">
                    Simulation Available
                  </span>
                )}

                {job?.simulation?.level && (
                  <span
                    className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-3 py-0.5 text-[10px] font-medium`}
                  >
                    {simulationLevel[Number(job.simulation.level)]}:{' '}
                    <b>{job.simulation.minute} mins</b>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="my-5 border-t border-[#c3c3c3]"></div>

        {/* Job Details */}
        <div className="flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
          <span className="flex items-center gap-1">
            <MoneyIcon className="h-3 w-3" />
            <span>
              {typeof job?.salary === 'string'
                ? job.salary
                : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
            </span>
          </span>
          <span className="flex items-center gap-1">
            <ClockIcon className="h-3 w-3" />
            <span>{jobType || '-'}</span>
          </span>
          <span className="flex items-center gap-1">
            <TechnologyIcon className="h-3 w-3" />
            {Array.isArray(job?.categories) && job?.categories?.length > 0 ? (
              <span>{job.categories.join(', ')}</span>
            ) : (
              '-'
            )}
          </span>
        </div>
      </div>

      <p className="mt-3 line-clamp-3 text-sm text-gray-600">
        {removeAllHTML(job.description || '')}
      </p>

      <div className="my-5 border-t border-[#c3c3c3]"></div>

      <div className="flex justify-end gap-2">
        {/* TODO: don't use this status. Use applicationStatus */}
        {(job as any).progress?.status === 'completed' ? (
          <Button size="sm" variant="outline">
            <Link href={`/profile/${user?.id}#applications`}>
              View Application
            </Link>
          </Button>
        ) : (
          <>
            {['cv', 'all'].includes(job?.applyMode as string) && (
              <ApplyCVButton
                jobId={job.jobId || ''}
                simId={job.simulation?.id || ''}
                onSuccess={onApplyCVSuccess}
                onErrorJobApplied={onErrorJobApplied}
                buttonProps={{ variant: 'outline', size: 'sm' }}
              />
            )}
            {['simulation', 'all'].includes(job?.applyMode as string) && (
              <>
                {/* <Button
              size="sm"
              className="bg-primary text-white"
              disabled={!job.simulation?.id}
              // onClick={handleStartSimulation}
            >
              Apply with Simulation
            </Button> */}
                <StartSimulationButton
                  simId={job.simulation?.id || ''}
                  jobId={job.jobId || ''}
                  onErrorJobApplied={onErrorJobApplied}
                  buttonProps={{
                    size: 'sm',
                    className: 'bg-primary text-white',
                    disabled: !job.simulation?.id,
                  }}
                >
                  Apply with Simulation
                </StartSimulationButton>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}
