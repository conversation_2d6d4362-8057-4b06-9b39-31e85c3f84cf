'use client';

import { useAtom } from 'jotai';
import TalentFeatureCard from './candidate-feature-card';
import CandidateResult from './candidate-result';
import { searchModeAtom } from '@/store/talent-atom';
import PageHeader from '@/shared/page-header';

export default function SearchTalent() {
  const [searchMode] = useAtom(searchModeAtom);

  return (
    <div>
      <CandidateResult />
    </div>
  );
}
