'use client';

import cn from '@/utils/class-names';
import {
  addDays,
  endOfMonth,
  endOfWeek,
  isAfter,
  startOfMonth,
  startOfWeek,
  subDays,
} from 'date-fns';
import React, { useState } from 'react';
import { Button, Popover } from 'rizzui';
import ReactDatePicker from '../date-picker';

type QuickRangeOption = 'yesterday' | 'today' | 'thisWeek' | 'thisMonth' | null;

interface DateRangePickerProps {
  onConfirm?: (dates: [Date | null, Date | null]) => void;
  onClear?: () => void;
  placeholder?: string;
  dateRange?: number;
  disableFuture?: boolean;
  triggerClassName?: string;
  prefixLabel?: string;
  TriggerComponent?: React.ComponentType<{ children: React.ReactNode }>;
}

const quickRanges = [
  { label: 'Yesterday', value: 'yesterday' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'thisWeek' },
  { label: 'This Month', value: 'thisMonth' },
];

export default function DateRangePicker({
  onConfirm,
  onClear,
  dateRange,
  disableFuture,
  placeholder = 'Select Date Range',
  triggerClassName = '',
  prefixLabel = '',
  TriggerComponent,
  ...props
}: DateRangePickerProps) {
  const [open, setOpen] = useState(false);
  const [selectedDates, setSelectedDates] = useState<
    [Date | null, Date | null]
  >([null, null]);
  const [maxDate, setMaxDate] = useState<Date | undefined>(
    disableFuture ? new Date() : undefined
  );
  // const [activeQuickRange, setActiveQuickRange] =
  //   useState<QuickRangeOption>(null);

  const handleSelectQuickRange = (range: QuickRangeOption) => {
    const today = new Date();
    let start: Date | null = null;
    let end: Date | null = null;

    switch (range) {
      case 'yesterday':
        start = subDays(today, 1);
        end = subDays(today, 1);
        break;
      case 'today':
        start = today;
        end = today;
        break;
      case 'thisWeek':
        start = startOfWeek(today, { weekStartsOn: 0 });
        end = endOfWeek(today, { weekStartsOn: 0 });
        break;
      case 'thisMonth':
        start = startOfMonth(today);
        end = endOfMonth(today);
        break;
      default:
        break;
    }

    setSelectedDates([start, end]);
    // setActiveQuickRange(range);
  };

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setSelectedDates(dates);
    if (dateRange !== undefined && dates[0] && !dates[1]) {
      let calculatedMaxDate = addDays(dates[0]!, dateRange);
      if (disableFuture && isAfter(calculatedMaxDate, new Date())) {
        calculatedMaxDate = new Date();
      }
      setMaxDate(calculatedMaxDate);
    }
    // setActiveQuickRange(null);
  };

  const displayValue =
    selectedDates[0] && selectedDates[1]
      ? `${prefixLabel}${selectedDates[0].toLocaleDateString()} - ${selectedDates[1].toLocaleDateString()}`
      : placeholder;

  return (
    <Popover isOpen={open} setIsOpen={setOpen}>
      <Popover.Trigger>
        {TriggerComponent ? (
          <TriggerComponent>{displayValue}</TriggerComponent>
        ) : (
          <Button
            variant="outline"
            className={cn('w-64 justify-between font-normal', triggerClassName)}
          >
            {displayValue}
          </Button>
        )}
      </Popover.Trigger>

      <Popover.Content className="w-auto rounded-lg border p-0 shadow-lg">
        <div className="flex overflow-hidden rounded-lg">
          <div className="flex w-32 flex-col border-r border-gray-200 bg-white p-2">
            {quickRanges.map((item) => (
              <Button
                key={item.value}
                variant={'outline'}
                className={cn(
                  'mb-2 w-full border-primary text-sm text-primary hover:bg-primary hover:text-white'
                )}
                onClick={() =>
                  handleSelectQuickRange(item.value as QuickRangeOption)
                }
              >
                {item.label}
              </Button>
            ))}
          </div>

          <div className="flex flex-col bg-white">
            <div className="px-2">
              <ReactDatePicker
                calendarClassName="!shadow-none !border-none"
                startDate={selectedDates[0]!}
                endDate={selectedDates[1]!}
                onChange={handleDateChange}
                selectsRange
                monthsShown={2}
                maxDate={maxDate}
                inline
                // todayButton
                {...props}
              />
            </div>
            <div className="flex justify-between border-t border-gray-200 bg-white p-3">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedDates([null, null]);
                  // setActiveQuickRange(null);
                  setMaxDate(disableFuture ? new Date() : undefined);
                  onClear?.();
                }}
              >
                Clear
              </Button>
              <Button
                className="bg-primary text-white hover:bg-primary/80"
                onClick={() => {
                  if (selectedDates[0] && selectedDates[1]) {
                    onConfirm?.(selectedDates);
                    setOpen(false);
                    setMaxDate(disableFuture ? new Date() : undefined);
                  }
                }}
              >
                Confirm
              </Button>
            </div>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
}
