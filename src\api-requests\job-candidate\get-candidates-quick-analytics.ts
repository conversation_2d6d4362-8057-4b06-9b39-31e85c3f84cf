import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { JobCandidateQueryKeys, QuickAnalyticsResponse } from './types';

interface GetOrgCandidatesQuickAnalyticsParams {
  orgId: string;
  appliedFrom?: Date;
  appliedTo?: Date;
  jobId?: string;
  simulationId?: string;
  applyMode?: string;
}

async function getData(
  params: GetOrgCandidatesQuickAnalyticsParams
): Promise<QuickAnalyticsResponse> {
  const { orgId, ...restParams } = params;
  const reps = await axiosInstance.get<QuickAnalyticsResponse>(
    API_ENDPONTS.GET_ORG_CANDIDATES_QUICK_ANALYTICS.replace(':orgId', orgId),
    {
      params: restParams,
    }
  );
  return reps.data;
}

export function useGetOrgCandidatesQuickAnalytics(
  params: GetOrgCandidatesQuickAnalyticsParams
) {
  return useQuery<QuickAnalyticsResponse>({
    queryKey: [
      JobCandidateQueryKeys.GET_ORG_CANDIDATES_QUICK_ANALYTICS,
      ...Object.values(params),
    ],
    queryFn: () => getData(params),
    enabled: !!params.orgId,
    retry: false,
  });
}
