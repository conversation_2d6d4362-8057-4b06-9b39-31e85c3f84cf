'use client';

import { useGetUserFavoriteJobs } from '@/api-requests/interaction/get-user-favorite-jobs';
import { useUnfavoriteJobs } from '@/api-requests/interaction/unfavorite-jobs';
import { Job } from '@/api-requests/job';
import { useUnfavoriteJob } from '@/api-requests/job/unfavorite-job';
import { LIMIT } from '@/api-requests/types';
import { userAtom } from '@/store/user-atom';
import { safeFormatDistanceToNow } from '@/utils/date';
import HeartRegularIcon from '@/views/icons/heart-regular';
import Pagination from '@/views/pagination';
import { useAtom } from 'jotai';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Checkbox, Loader, Table, Tooltip } from 'rizzui';

const badgeClasses: Record<string, string> = {
  failed: 'bg-[#E8E8E8] text-[#161616]',
  inProgress: 'bg-[#FFEADA] text-[#EE6C0F]',
  passed: 'bg-[#98FFDC] text-[#008457]',
};

export default function Favourite() {
  const [user] = useAtom(userAtom);
  const [page, setPage] = useState(1);
  const [selectedJobs, setSelectedJobs] = useState<Job[]>([]);

  const { data, isLoading, refetch } = useGetUserFavoriteJobs(
    user?.id as string,
    {
      page,
      limit: LIMIT,
    }
  );

  const { mutateAsync: mutateAsyncUnfavorite, isPending: isPendingUnfavorite } =
    useUnfavoriteJob();
  const {
    mutateAsync: mutateAsyncUnfavoriteJobs,
    isPending: isPendingUnfavoriteJobs,
  } = useUnfavoriteJobs();

  const handleCheckboxChange = (checked: boolean, job: Job | string) => {
    if (job === 'all') {
      if (checked) {
        setSelectedJobs(data?.data || []);
      } else {
        setSelectedJobs([]);
      }
      return;
    } else {
      setSelectedJobs((prev) => {
        if (checked) {
          return [...prev, job as Job];
        } else {
          return prev.filter((c) => c.jobId !== (job as Job).jobId);
        }
      });
    }
  };

  const handleUnfavoriteAllJobs = async () => {
    const targetIds = selectedJobs.map((job) => job.jobId);
    if (targetIds.length === 0) {
      toast.error('No jobs selected');
      return;
    }

    const reps = await mutateAsyncUnfavoriteJobs({
      userId: user?.id as string,
      targetIds,
      targetType: 'job',
    });

    if (reps) {
      toast.success('Jobs unfavorited');
      setSelectedJobs([]);
      refetch();
    } else {
      toast.error('Failed to unfavorite jobs');
    }
  };

  const handleUnfavoriteJob = async (job: Job) => {
    const resp = await mutateAsyncUnfavorite(job.jobId);
    if (resp) {
      toast.success('Job unfavorited');
      if (selectedJobs.some((c) => c.jobId === job.jobId)) {
        setSelectedJobs((prev) => prev.filter((c) => c.jobId !== job.jobId));
      }
      refetch();
    } else {
      toast.error('Failed to unfavorite job');
    }
  };

  return (
    <div>
      {selectedJobs.length ? (
        <div className="mb-4 flex items-center gap-3">
          <span>{selectedJobs.length} selected</span>
          <Tooltip content="Unfavorite all selected jobs" color="invert">
            <button
              className="flex items-center gap-2 rounded border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
              onClick={handleUnfavoriteAllJobs}
              disabled={isPendingUnfavorite || isPendingUnfavoriteJobs}
            >
              <HeartRegularIcon className="h-4 w-4" />
            </button>
          </Tooltip>
        </div>
      ) : (
        ''
      )}

      <Table variant="modern">
        <Table.Header className="rounded-t-xl !bg-white">
          <Table.Row className="rounded-t-xl border-b border-b-[#c3c3c3]">
            <Table.Head className="w-4">
              <Checkbox
                variant="flat"
                size="sm"
                checked={
                  selectedJobs.length === data?.data?.length &&
                  data?.data?.length > 0
                }
                onChange={(e) => handleCheckboxChange(e.target.checked, 'all')}
              />
            </Table.Head>
            <Table.Head>Company</Table.Head>
            <Table.Head>Title</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {data?.data.map((job, index) => (
            <Table.Row key={index} className='border-b border-b-[#c3c3c3]'>
              <Table.Cell>
                <Checkbox
                  variant="flat"
                  size="sm"
                  checked={selectedJobs.some((c) => c.jobId === job?.jobId)}
                  onChange={(e) => handleCheckboxChange(e.target.checked, job)}
                />
              </Table.Cell>

              <Table.Cell>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div>
                      {job?.companyLogoUrl ? (
                        <Image
                          src={job.companyLogoUrl}
                          alt={job.companyName || 'Company Logo'}
                          width={60}
                          height={60}
                          className="h-15 w-15 rounded-full object-contain"
                          loader={({ src }) => src}
                        />
                      ) : (
                        <div
                          className="!h-15 !w-15 rounded-full bg-gray-100"
                          style={{ width: '60px', height: '60px' }}
                        />
                      )}
                    </div>

                    <div>
                      <div>
                        {job.orgId ? (
                          <Link
                            href={`/org/${job?.orgId}`}
                            target="_blank"
                            className="hover:underline"
                          >
                            <p className="font-bold text-gray-700">
                              {job?.companyName}
                            </p>
                          </Link>
                        ) : (
                          <p className="font-bold text-gray-700">
                            {job?.companyName}
                          </p>
                        )}

                        <p className="text-[12px] text-gray-500">
                          {job?.location} •{' '}
                          {safeFormatDistanceToNow(new Date(job?.postedTime), {
                            addSuffix: true,
                          })}
                        </p>
                      </div>

                      {/* <div className="flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
                        <span className="flex items-center gap-1">
                          <MoneyIcon className="h-3 w-3" />
                          <span>{job?.salary || '-'}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <ClockIcon className="h-3 w-3" />
                          <span>{getJobTypeString(job as never) || '-'}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <TechnologyIcon className="h-3 w-3" />
                          {Array.isArray(job?.categories) &&
                          job?.categories?.length > 0 ? (
                            <span>{job.categories.join(', ')}</span>
                          ) : (
                            '-'
                          )}
                        </span>
                        {job?.simulation?.level && (
                          <span
                            className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-1`}
                          >
                            {simulationLevel[Number(job.simulation.level)]}:{' '}
                            <b>{job.simulation.minute} mins</b>
                          </span>
                        )}
                      </div> */}
                    </div>
                  </div>

                  {/* <div>
                    <div>AI review</div>
                    <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
                      {job.aiReview.map((review, idx) => (
                        <li key={idx}>{review}</li>
                      ))}
                    </ul>
                    <div>AI Verdict: {job.aiVerdict}</div>
                  </div> */}

                  <div className="flex gap-4">
                    <button
                      className="flex items-center gap-2 rounded border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
                      onClick={() =>
                        window.open(`/find-jobs?id=${job.jobId}`, '_blank')
                      }
                    >
                      <span>View Detail</span>
                    </button>
                    <button
                      className="flex items-center gap-2 rounded border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
                      disabled={isPendingUnfavorite || isPendingUnfavoriteJobs}
                      onClick={() => handleUnfavoriteJob(job)}
                    >
                      <HeartRegularIcon className="h-4 w-4" />
                      <span>Unfavourite</span>
                    </button>
                  </div>
                </div>
              </Table.Cell>

              <Table.Cell>
                <span className="text-sm font-bold">{job.title}</span>
              </Table.Cell>
            </Table.Row>
          ))}

          {isLoading ? (
            <Table.Row>
              <Table.Cell colSpan={3} className="h-40 text-center">
                <div className="flex min-h-40 items-center justify-center">
                  <Loader className="h-8 w-8" />
                </div>
              </Table.Cell>
            </Table.Row>
          ) : (
            data?.data?.length === 0 && (
              <Table.Row>
                <Table.Cell colSpan={7} className="text-center">
                  <div className="text-gray-500">No favourite jobs</div>
                </Table.Cell>
              </Table.Row>
            )
          )}
        </Table.Body>
      </Table>

      <hr className="border-t border-[#c3c3c3] pt-4" />

      <Pagination
        total={data?.meta?.total || 0}
        current={page}
        pageSize={LIMIT}
        defaultCurrent={1}
        showLessItems={true}
        onChange={(page: number) => setPage(page)}
        prevIconClassName="py-0 text-gray-500 !leading-[26px]"
        nextIconClassName="py-0 text-gray-500 !leading-[26px]"
        variant="solid"
        className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
        // disabled={isLoading}
      />
    </div>
  );
}
