import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { UserApplication, UserProfileQueryKeys } from './types';
import { ApiListResponse } from '../types';

type GetUserApplicationsInput = {
  limit: number;
  page: number;
  sortAppliedAt?: 'asc' | 'desc';
  status?: string;
};

export async function getUserApplications(
  params: GetUserApplicationsInput
): Promise<ApiListResponse<UserApplication>> {
  const response = await axiosInstance.get<ApiListResponse<UserApplication>>(
    API_ENDPONTS.GET_MY_APPLICATIONS,
    { params }
  );
  return response.data;
}

export function useGetUserApplications(params: GetUserApplicationsInput) {
  return useQuery<ApiListResponse<UserApplication>>({
    queryKey: [
      UserProfileQueryKeys.GET_USER_APPLICATIONS,
      params.status,
      params.page,
      params.sortAppliedAt,
    ],
    queryFn: () => getUserApplications(params),
    enabled: true,
  });
}
