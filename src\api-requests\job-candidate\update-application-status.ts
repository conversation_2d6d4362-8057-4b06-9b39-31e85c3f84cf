import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export async function updateApplicationStatus(payload: {
  applicationId: string;
  orgId: string;
  [key: string]: any;
}) {
  // TODO: Use Patch instead (?)
  const { applicationId, ...restData } = payload;
  const response = await axiosInstance.patch(
    API_ENDPONTS.UPDATE_APPLICATION_STATUS_ORG.replace(':id', applicationId),
    restData
  );
  return response.data;
}

export const useUpdateApplicationStatus = () => {
  return useMutation({
    mutationFn: (payload: {
      applicationId: string;
      orgId: string;
      [key: string]: any;
    }) => updateApplicationStatus(payload),
    onSuccess: () => {
      toast.success('Status updated successfully.');
    },
    onError: () => {
      toast.error('Failed to update status.');
    },
  });
};
