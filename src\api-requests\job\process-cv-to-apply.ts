import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation } from '@tanstack/react-query';
import { JobQuickQuestion } from './types';

export interface CandidateQuickQuestion extends JobQuickQuestion {
  answer: any;
}

export interface ProcessCVToApplyResponse {
  id: string;
  status: 'passed' | 'failed';
  position?: string;
  overview: string[];
  feedback: string[];
  score: number;
  matchPercentage: number;
  tasks: { title: string; description: string; exampleSubmission: string }[];
  quickQuestions?: CandidateQuickQuestion[];
  quickQuestionsBonus: number;
}

async function processCVToApply(params: {
  jobId: string;
  simulationId: string;
  cvFile?: File;
  useProfileCV?: boolean;
}): Promise<ProcessCVToApplyResponse | null> {
  // await new Promise((resolve) => setTimeout(resolve, 10000)); // simulate 6s delay
  // return null;

  const formData = new FormData();
  formData.append('jobId', params.jobId);
  formData.append('simulationId', params.simulationId);

  if (params.cvFile) {
    formData.append('cvFile', params.cvFile);
  } else if (params.useProfileCV) {
    formData.append('useProfileCV', 'true');
  }

  const response = await axiosInstance.post<ProcessCVToApplyResponse>(
    API_ENDPONTS.PROCESS_CV_TO_APPLY,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
}

async function applyCV(params: {
  processId: string;
  tasks?: {
    title: string;
    description: string;
    submission: {
      submittedAt: Date;
      content: string;
    };
  }[];
  quickQuestions?: CandidateQuickQuestion[];
}): Promise<any> {
  const response = await axiosInstance.post<any>(API_ENDPONTS.APPLY_CV, params);
  return response.data;
}

export const useProcessCVToApply = () => {
  return useMutation({
    mutationFn: (params: {
      jobId: string;
      simulationId: string;
      cvFile?: File;
      useProfileCV?: boolean;
    }) => processCVToApply(params),
    // onError: (error: any) => {
    //   // let errMsg = 'Failed to process CV. Please try again later.';
    //   // if (error instanceof AxiosError && error.status === 400) {
    //   //   errMsg = error.response?.data?.message || errMsg;
    //   // }
    //   // toast.error(errMsg);
    // },
  });
};

export const useApplyCV = () => {
  return useMutation({
    mutationFn: (params: {
      processId: string;
      tasks?: {
        title: string;
        description: string;
        submission: {
          submittedAt: Date;
          content: string;
        };
      }[];
      quickQuestions?: CandidateQuickQuestion[];
    }) => applyCV(params),
  });
};
