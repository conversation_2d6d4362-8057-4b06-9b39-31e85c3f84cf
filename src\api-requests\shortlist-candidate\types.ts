import { JobCandidate } from '../job-candidate/types';
import { Organization } from '../organization';

export const ShortlistCandidateQueryKeys = {
  CREATE_SHORTLIST_CANDIDATE: 'createShortlistCandidate',
  CREATE_SHORTLIST_CANDIDATE_MANUAL: 'createShortlistCandidateManual',
  BULK_CREATE_SHORTLIST_CANDIDATE: 'bulkCreateShortlistCandidate',
  BULK_DELETE_SHORTLIST_CANDIDATE: 'bulkDeleteShortlistCandidate',
  DELETE_SHORTLIST_CANDIDATE: 'deleteShortlistCandidate',
  DELETE_SHORTLIST_CANDIDATE_BY_ID_AND_ORG:
    'deleteShortlistCandidateByIdAndOrg',
  LIST_SHORTLIST_CANDIDATE_BY_ORG: 'listShortlistCandidateByOrg',
};

export interface CreateParams {
  candidateId: string;
  orgId: string;
  shortlistId: string;
}

export interface CreateManualParams {
  jobId: string;
  orgId: string;
  shortlistId: string;
  note?: string;
  userId: string;
  email: string;
}
export interface BulkCreateParams {
  candidateIds: string[];
  orgId: string;
  shortlistId: string;
}

export interface ShortlistCandidate extends JobCandidate {
  candidateId: string;
  shortlistId: string;
  shortlist?: {
    _id: string;
    name: string;
  };
  org?: Organization;
  source: 'applied' | 'manual';
  note?: string;
}
