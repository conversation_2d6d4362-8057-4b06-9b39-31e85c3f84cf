'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import RightIcon from '@/views/icons/right';

type CrumbItem = {
  name: string;
  href: string;
};

type BreadcrumbProps = {
  items?: CrumbItem[];
};

function formatSegment(segment: string): string {
  return decodeURIComponent(segment)
    .replace(/-/g, ' ')
    .replace(/\b\w/g, (c) => c.toUpperCase());
}

export default function Breadcrumb({ items }: BreadcrumbProps) {
  const pathname = usePathname();

  const autoSegments = pathname.split('/').filter(Boolean);

  const autoCrumbs: CrumbItem[] = autoSegments.map((segment, idx) => {
    const href = '/' + autoSegments.slice(0, idx + 1).join('/');
    return {
      name: formatSegment(segment),
      href,
    };
  });

  const crumbsToRender = items ?? autoCrumbs;

  return (
    <nav className="flex items-center space-x-2 text-gray-600">
      {crumbsToRender.map((crumb, idx) => (
        <div key={idx} className="flex items-center space-x-2">
          {idx > 0 && (
            <span>
              <RightIcon />
            </span>
          )}
          {idx === crumbsToRender.length - 1 ? (
            <span className="font-bold">{crumb.name}</span>
          ) : (
            <Link href={crumb.href} className="text-gray-600 hover:underline">
              {crumb.name}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
}
