export const SimulationQueryKeys = {
  GET_ORG_HOME_SIMULATIONS: 'getOrgHomeSimulations',
  GET_ORG_PUBLIC_SIMULATION: 'getOrgPublicSimulation',
  GET_PUBLIC_SIMULATION: 'getPublicSimulation',
  GET_ORG_FEATURED_SIMULATIONS: 'getOrgFeaturedSimulations',
  GET_ORG_ADMIN_SIMULATIONS: 'getOrgAdminSimulations',
};

export interface HomeSimulationCandidate {
  email: string;
  name: string;
  id: string;
  avatar: string;
}

interface Progress {
  status: 'active' | 'completed' | 'not-started'; // ví dụ: "active"
}

export interface HomeSimulation {
  _id: string;
  simulationId: string;
  description: string;
  level: number;
  minute: number;
  name: string;
  industry?: string;
  softSkills?: string[];
  hardSkills?: string[];
  benefits?: string[];
  completedCount?: number;
  joinedCount?: number;
  id: string;
  candidates?: HomeSimulationCandidate[];
  progress?: Progress;
  banner?: string;
}

export interface AdminSimulation {
  _id: string;
  simulationId: string;
  description: string;
  level: number;
  minute: number;
  name: string;
  industry?: string;
  softSkills?: string[];
  hardSkills?: string[];
  benefits?: string[];
  completedCount?: number;
  joinedCount?: number;
  id: string;
  banner?: string;
  status: 'published' | 'unpublished' | 'pending';
  tasks?: { id?: string; title?: string; content?: string }[];
}
