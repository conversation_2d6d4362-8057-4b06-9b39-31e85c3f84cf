/* @import "tailwindcss"; */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* --background: #ffffff;
  --foreground: #171717;
  --primary-rgb: 27 28 30; */

:root {
  --background: 255 255 255; /* #ffffff */
  --foreground: 23, 23, 23; /* #171717 */
  --muted: 227 227 227; /* #e3e3e3 */
  --muted-foreground: 146 146 146; /* #929292 */

  /*
    * primary colors
    */
  --primary-lighter: 227 227 227; /* #e3e3e3 */
  --primary-default: 27 28 30;
  --primary-dark: 0 0 0; /* #000000 */
  --primary-foreground: 255 255 255; /* #ffffff */

  /*
    * secondary colors
    */
  --secondary-lighter: 221 227 255; /* #dde3ff */
  --secondary-default: 78 54 245; /* #4e36f5 */
  --secondary-dark: 67 42 216; /* #432ad8 */
  --secondary-foreground: 255 255 255; /* #ffffff */

  /*
    * danger colors
    */
  --red-lighter: 247 212 214; /* #f7d4d6 */
  --red-default: 238 0 0; /* #e00 */
  --red-dark: 197 0 0; /* #c50000 */

  /*
    * warning colors
    */
  --orange-lighter: 255 239 207; /* #ffefcf */
  --orange-default: 245 166 35; /* #f5a623 */
  --orange-dark: 171 87 10; /* #ab570a */

  /*
    * info colors
    */
  --blue-lighter: 211 229 255; /* #d3e5ff */
  --blue-default: 0 112 243; /* #0070f3 */
  --blue-dark: 7 97 209; /* #0761d1 */

  /*
    * success colors
    */
  --green-lighter: 185 249 207; /* #b9f9cf */
  --green-default: 17 168 73; /* #11a849 */
  --green-dark: 17 132 60; /* #11843c */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* 
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

/* --------------------------------- */
/* dark theme */
/* --------------------------------- */
[data-theme='dark'] {
  --background: 8 9 14; /* #08090e */
  --foreground: 223 223 223; /* #dfdfdf */
  --muted: 51 51 51; /* #333333 */
  --muted-foreground: 102 102 102; /* #666666 */

  /*
    * primary colors
    */
  --primary-lighter: 34 34 34; /* #222222 */
  --primary-default: 241 241 241; /* #f1f1f1 */
  --primary-dark: 255 255 255; /* #ffffff */
  --primary-foreground: 0 0 0; /* #000000 */

  /*
    * secondary colors
    */
  --secondary-lighter: 31 22 90; /* #1f165a */
  --secondary-dark: 193 203 255; /* #c1cbff */

  /*
    * danger colors
    */
  --red-lighter: 80 0 0; /* #500000 */
  --red-dark: 255 193 193; /* #ffc1c1 */

  /*
    * warning colors
    */
  --orange-lighter: 68 29 4; /* #441d04 */
  --orange-dark: 252 234 139; /* #fcea8b */

  /*
    * info colors
    */
  --blue-lighter: 13 51 94; /* #0d335e */
  --blue-dark: 181 233 255; /* #b5e9ff */

  /*
    * success colors
    */
  --green-lighter: 3 48 22; /* #033016 */
  --green-dark: 185 249 207; /* #b9f9cf */

  /* here you can customize other colors for dark theme if design required */
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* .text-primary {
  color: var(--primary);
}

.bg-primary {
  background-color: var(--primary);
}

.border-primary {
  border-color: var(--primary);
} */

/* Checkbox styles */
.rizzui-checkbox-input {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

.rizzui-checkbox-input:checked {
  background-color: #ffffff;
  /* border: 1px solid var(--primary); */
  border: 1px solid #d1d5db;
}

.rizzui-checkbox-input:checked + .rizzui-checkbox-icon {
  /* color: var(--primary); */
  /* stroke: var(--primary); */
  /* background-color: #ffffff; */
  top: 0 !important;
  stroke-width: 3px;
  /* border: 1px solid #d1d5db; */
}

.rizzui-input-container {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}
.rizzui-input-container.is-focus,
.rizzui-input-container:focus-within {
  --tw-ring-offset-shadow: none !important;
  --tw-ring-shadow: none !important;
  box-shadow: none !important;
}
.rizzui-input-container:focus {
  outline: var(--primary);
}

.rizzui-password-container {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}
.rizzui-password-container.is-focus,
.rizzui-password-container:focus-within {
  --tw-ring-offset-shadow: none !important;
  --tw-ring-shadow: none !important;
  box-shadow: none !important;
}
.rizzui-password-container:focus {
  outline: var(--primary);
}

.rizzui-textarea-field {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}
.rizzui-textarea-field.is-focus,
.rizzui-textarea-field:focus-within {
  --tw-ring-offset-shadow: none !important;
  --tw-ring-shadow: none !important;
  box-shadow: none !important;
}
.rizzui-textarea-field:focus {
  outline: var(--primary);
}

.rizzui-multi-select-button {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}

.rizzui-multi-select-button:focus {
  outline: var(--primary);
}

/* .rizzui-multi-select-options {
  background: #ffffff;
  border-color: none;
} */

.rizzui-radio-field {
  box-shadow: none;
}

.rizzui-modal-container,
.rizzui-dropdown-menu,
.rizzui-select-options,
.rizzui-multi-select-options {
  box-shadow: none;
  background-color: white;
  border: 1px solid #c3c3c3;
  border-radius: 12px;
}
.rizzui-select-option:hover {
  background-color: #ebebeb;
}

/* .rizzui-switch-knob {
  background-color: var(--primary);
}

.rizzui-switch input[disabled] + span .rizzui-switch-knob,
.rizzui-switch input:disabled + span .rizzui-switch-knob {
  background-color: #868990;
}

.rizzui-switch span[class*='bg-muted'] {
  background-color: #e3e3e3;
}

.rizzui-switch input:checked + span {
  background-color: #e3e3e3 !important;
}

.peer\/switch:checked ~ .peer-checked\/switch\:bg-primary {
  background-color: #e3e3e3 !important;
} */

.rizzui-radio-field {
  box-shadow: none;
  position: relative;
  border-color: var(--primary);
}

.rizzui-radio-field:checked {
  border: 1px solid var(--primary);
}

.rizzui-radio-field:checked::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border-radius: 50%;
  background: var(--primary);
  display: block;
}

.rizzui-input-clear-btn:hover {
  background-color: var(--primary);
  color: white;
}

.rizzui-select-button {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}

.rizzui-select-button:focus {
  outline: var(--primary);
}

.rizzui-select-search {
  border: 1px solid #c3c3c3;
  outline: 1px solid #c3c3c3;
  background: #ffffff;
  border-radius: 12px;
}

.rizzui-select-search:focus {
  outline: var(--primary);
}

.rizzui-table-head {
  color: var(--primary) !important;
  border-radius: 12px;
}

.rizzui-table-row .rizzui-table-cell,
.rizzui-table .rizzui-table-cell,
[class*='rizzui-table'] td,
[class*='rizzui-table'] th {
  vertical-align: top !important;
  align-items: flex-start !important;
}

.rizzui-button {
  border-radius: 12px;
}

/* ................................................... */
/* React Datepicker Styling */
/* ................................................... */

/* .react-datepicker-wrapper {
  width: 100%;
}
.react-datepicker__close-icon::after {
  background-color: var(--foreground) !important;
}

.react-datepicker__day--in-range:not(.react-datepicker__day--range-start):not(
    .react-datepicker__day--range-end
  ) {
  background-color: #f1f5f9 !important;
  color: var(--primary);
}

.react-datepicker__day--today {
  background-color: #f1f5f9;
  border-radius: 0.3rem;
}

.react-datepicker__day--in-selecting-range:not(
  .react-datepicker__day--keyboard-selected
) {
  background-color: #f1f5f9 !important;
  color: var(--primary);
}

.react-datepicker__day--keyboard-selected.react-datepicker__day--range-end {
  background-color: var(--primary) !important;
  color: #fff;
}

.react-datepicker__day--selected,
.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background-color: var(--primary) !important;
}
.react-datepicker__time-list-item--selected {
  background-color: var(--primary) !important;
}
.react-datepicker__day--keyboard-selected {
  background-color: var(--primary) !important;
  color: #fff !important;
}
.react-datepicker {
  box-shadow: none !important;
  border: none !important;
}
.react-datepicker__month-container {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.react-datepicker__navigation {
  top: 6px !important;
}
.react-datepicker__navigation-icon--previous {
  right: 2px !important;
}
.react-datepicker__navigation-icon--next {
  left: 2px !important;
}
.react-datepicker__header {
  background-color: #ffffff !important;
} */

/* ................................................... */
/* React Datepicker Styling */
/* ................................................... */
.react-datepicker-popper .react-datepicker {
  @apply bg-white text-gray-600;
}

/* month container */
.react-datepicker .react-datepicker__month-container {
  @apply px-3;
}

/* time container */
.react-datepicker .react-datepicker__time-container {
  @apply w-auto border-l-0 pr-3.5;
}

.react-datepicker-popper
  .react-datepicker__time-container
  .react-datepicker__time {
  @apply bg-gray-50;
}

/* .react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item:hover {
  @apply dark:bg-muted;
} */

/* header */
.react-datepicker .react-datepicker__header {
  @apply border-b-transparent bg-white;
}

/* current month name in header */
/* .react-datepicker .react-datepicker-year-header {
  @apply dark:text-gray-700;
} */

.react-datepicker .react-datepicker__current-month {
  @apply mb-3 text-base font-medium;
}

/* sun-sat day names in header */
.react-datepicker .react-datepicker__day-names div {
  @apply m-1.5 text-sm text-gray-700;
}

/* previous month button */
.react-datepicker .react-datepicker__navigation--previous {
  @apply ml-6 rtl:mr-6;
}

/* next month button */
.react-datepicker .react-datepicker__navigation--next {
  @apply mr-6 rtl:ml-6;
}

/* icon in previous month button */
.react-datepicker .react-datepicker__navigation-icon--previous {
  @apply right-0.5;
}

/* icon in next month button */
.react-datepicker .react-datepicker__navigation-icon--next {
  @apply left-0.5;
}

/* each day */
.react-datepicker .react-datepicker__day {
  @apply m-1.5 text-sm leading-7 text-gray-700;
  @apply hover:rounded-full hover:bg-gray-100 hover:text-gray-900;
}

/* outside month */
.react-datepicker .react-datepicker__day--outside-month {
  @apply text-gray-500;
}

/* keyboard selected */
.react-datepicker .react-datepicker__day--keyboard-selected {
  @apply bg-gray-50;
}

/* today */
.react-datepicker .react-datepicker__day--today {
  @apply border-muted rounded-full border bg-gray-50 leading-[26px] text-gray-900;
  /* @apply hover:border-gray-600 hover:bg-gray-50; */
  @apply hover:!rounded-full hover:bg-gray-300;
}

/* while selecting */
.react-datepicker div.react-datepicker__day--in-selecting-range {
  @apply rounded-full bg-gray-200 text-gray-900;
}

.react-datepicker div.react-datepicker__day--in-selecting-range:hover,
.react-datepicker div.react-datepicker__day--keyboard-selected:hover,
.react-datepicker div.react-datepicker__day--in-range:hover {
  @apply rounded-full bg-gray-300 text-gray-900;
}

.react-datepicker div.react-datepicker__year-text--in-selecting-range {
  @apply bg-gray-200 text-gray-900;
}

.react-datepicker
  div.react-datepicker__year-text--in-selecting-range.react-datepicker__year-text--disabled {
  @apply text-muted bg-transparent;
}

/* in range */
.react-datepicker .react-datepicker__day--in-range {
  @apply rounded-full bg-gray-200 text-gray-900;
}

.react-datepicker .react-datepicker__year-text--in-range {
  @apply bg-gray-200 text-gray-900;
}

/* selected */
.react-datepicker .react-datepicker__day--range-start,
.react-datepicker .react-datepicker__day--range-end,
.react-datepicker .react-datepicker__day--selected {
  @apply rounded-full border-none bg-gray-900 font-normal leading-7 text-gray-50;
  @apply hover:!rounded-full hover:!bg-gray-900/80 hover:!text-gray-50;
}

.react-datepicker .react-datepicker__year-text--range-end {
  @apply border-none bg-gray-900 font-normal text-gray-50 hover:bg-gray-900/80 hover:text-gray-50;
}

/* time list */
.react-datepicker .react-datepicker__time-list {
  @apply !h-[247px];
}

/* time item */
.react-datepicker .react-datepicker__time-list-item {
  @apply my-2 rounded text-sm text-gray-500;
  @apply hover:bg-gray-100 hover:text-gray-900;
}

/* selected time item */
.react-datepicker .react-datepicker__time-list-item--selected {
  @apply !bg-gray-100 !font-medium !text-gray-900;
}

/* time only box */
.react-datepicker-popper .react-datepicker-time__header {
  @apply text-gray-700;
}
.react-datepicker-popper
  .react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box {
  width: 90px;
}
.react-datepicker--time-only
  .react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box {
  @apply w-auto;
}

/* time only item */
.react-datepicker--time-only .react-datepicker__time-list-item {
  @apply rounded-none;
}

/* month picker text */
.react-datepicker .react-datepicker__month-text {
  @apply w-0 px-5 py-1.5;
}

/* keyboard selected month in month picker */
.react-datepicker .react-datepicker__month-text--keyboard-selected {
  @apply bg-gray-100 text-gray-900;
}

/* month in range */
.react-datepicker .react-datepicker__month--in-range {
  @apply bg-gray-200 text-gray-700;
}

/* hover on selected months in range */
.react-datepicker
  .react-datepicker__month-text.react-datepicker__month--in-range:hover {
  @apply bg-gray-200 text-gray-900;
}

/* selected month in range */
.react-datepicker .react-datepicker__month--range-start,
.react-datepicker .react-datepicker__month--range-end,
.react-datepicker .react-datepicker__month--selected {
  @apply bg-gray-900 font-normal text-gray-50;
}

/* hover on selected range start and end month */
.react-datepicker
  .react-datepicker__month-text.react-datepicker__month--selected:hover,
.react-datepicker
  .react-datepicker__month-text.react-datepicker__month--range-start:hover,
.react-datepicker
  .react-datepicker__month-text.react-datepicker__month--range-end:hover {
  @apply bg-gray-900/80 text-gray-50;
}

/* year wrapper in year picker */
.react-datepicker .react-datepicker__year-wrapper {
  @apply inline-block max-w-[220px];
}

/* year text in year picker */
.react-datepicker .react-datepicker__year-text {
  @apply w-auto px-5 py-1.5 text-gray-600;
}

.react-datepicker .react-datepicker__year-text--range-end {
  @apply text-gray-50;
}

/* keyboard selected year in year picker */
.react-datepicker .react-datepicker__year-text--keyboard-selected {
  @apply bg-gray-200 text-gray-900;
  @apply hover:bg-muted hover:text-gray-900;
}

/* selected year & month in year picker */
.react-datepicker
  .react-datepicker__year-text.react-datepicker__year-text--selected,
.react-datepicker
  .react-datepicker__month-text.react-datepicker__month-text--selected {
  @apply bg-gray-900 text-gray-50;
  @apply hover:bg-gray-900/80 hover:text-gray-50;
}

/* year and month dropdown arrow */
.react-datepicker .react-datepicker__year-read-view--down-arrow,
.react-datepicker .react-datepicker__month-read-view--down-arrow {
  @apply border-muted top-[5px] h-[7px] w-[7px] border-r-[1.5px] border-t-[1.5px];
}

/* sub-header containing year and month dropdown */
.react-datepicker
  .react-datepicker__current-month--hasYearDropdown.react-datepicker__current-month--hasMonthDropdown
  ~ .react-datepicker__header__dropdown {
  @apply divide-muted my-2 grid grid-cols-2 divide-x text-sm;
}

/* month and year dropdown button in sub-header */
.react-datepicker .react-datepicker__month-dropdown-container--scroll,
.react-datepicker .react-datepicker__year-dropdown-container--scroll {
  @apply inline-flex justify-center;
}

/* month and year dropdown */
.react-datepicker .react-datepicker__year-dropdown,
.react-datepicker .react-datepicker__month-dropdown {
  @apply top-auto w-2/5 border border-gray-100 bg-gray-50 shadow-md;
}

/* year dropdown */
.react-datepicker .react-datepicker__year-dropdown {
  @apply h-80;
}

/* month dropdown */
.react-datepicker .react-datepicker__month-dropdown {
  @apply py-3;
}

/* month and year option */
.react-datepicker .react-datepicker__month-option,
.react-datepicker .react-datepicker__year-option {
  @apply my-1 py-1 text-sm text-gray-600;
  @apply hover:bg-gray-100 hover:text-gray-900;
}

/* first and last type of month and year option */
.react-datepicker .react-datepicker__month-option:first-of-type,
.react-datepicker .react-datepicker__month-option:last-of-type,
.react-datepicker .react-datepicker__year-option:first-of-type,
.react-datepicker .react-datepicker__year-option:last-of-type {
  @apply rounded-none;
}

/* selected month and year in dropdown */
.react-datepicker .react-datepicker__month-option--selected_month,
.react-datepicker .react-datepicker__year-option--selected_year {
  @apply bg-gray-200 text-gray-900;
  @apply [&>span]:hidden;
}
.react-datepicker .react-datepicker__day:hover,
.react-datepicker .react-datepicker__month-text:hover,
.react-datepicker .react-datepicker__quarter-text:hover,
.react-datepicker .react-datepicker__year-text:hover {
  @apply bg-gray-200 text-gray-900;
}

/* disabled item */
.react-datepicker .react-datepicker__day--disabled {
  @apply text-muted hover:text-muted cursor-not-allowed hover:bg-transparent;
}

.react-datepicker .react-datepicker__year-text--disabled {
  @apply text-muted cursor-not-allowed hover:bg-transparent;
}

.react-datepicker__close-icon {
  @apply !right-[6px];
}

.react-datepicker__close-icon::after {
  @apply !bg-primary;
}

/* ................................................... */
/* END React Datepicker Styling */
/* ................................................... */
