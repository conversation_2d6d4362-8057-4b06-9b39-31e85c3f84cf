'use client';

import { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import <PERSON>rop<PERSON>, { Area } from 'react-easy-crop';
import type { ReactNode } from 'react';

// ⬇️ Adjust the import path to your existing common uploader
import ImageUploader, { type ImageUploaderProps } from '@/views/image-uploader';
import { ActionIcon, Button, Modal, Title } from 'rizzui';
import CloseIcon from '@/views/icons/close';

export type ImageUploaderWithCropProps = Omit<
  ImageUploaderProps,
  'onChange'
> & {
  /**
   * Called with the FINAL cropped file (or null if user cancels)
   */
  onChange: (file: File | null) => void;
  /** Aspect ratio for the crop frame. Default 1 (square) */
  aspect?: number;
  /** Round crop mask (useful for avatars). Default 'rect' */
  cropShape?: 'rect' | 'round';
  /** Zoom range, default [1, 4] */
  zoomRange?: [number, number];
  /** Output mime type. If omitted, keep original file type */
  outputType?: 'image/jpeg' | 'image/png' | 'image/webp';
  /** Canvas export quality (0..1). Default 0.92 (when lossy) */
  quality?: number;
  /** Optional title/UI text */
  modalTitle?: string;
};

export default function ImageUploaderWithCrop({
  onChange,
  onError,
  accept = 'image/png,image/jpeg,image/jpg,image/gif,image/webp',
  maxSize = 1024 * 1024,
  multiple = false,
  disabled = false,
  children,
  aspect = 1,
  cropShape = 'round',
  zoomRange = [1, 4],
  outputType,
  quality = 0.92,
  modalTitle = 'Crop your image',
}: ImageUploaderWithCropProps) {
  const [isCropping, setIsCropping] = useState(false);
  const [imageURL, setImageURL] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [open, setOpen] = useState(false);

  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  useEffect(() => {
    if (isCropping && imageURL) {
      setOpen(true);
    }
  }, [isCropping, imageURL]);

  const cleanup = useCallback(() => {
    if (imageURL) URL.revokeObjectURL(imageURL);
    setImageURL(null);
    setOriginalFile(null);
    setIsCropping(false);
    setZoom(1);
    setCrop({ x: 0, y: 0 });
    setCroppedAreaPixels(null);
    setOpen(false);
  }, [imageURL]);

  const handlePreSelect = useCallback((file: File | null) => {
    if (!file) return;
    // Open crop modal and hold the file until confirmed
    const url = URL.createObjectURL(file);
    setOriginalFile(file);
    setImageURL(url);
    setIsCropping(true);
  }, []);

  const onCropComplete = useCallback((_: Area, croppedPixels: Area) => {
    setCroppedAreaPixels(croppedPixels);
  }, []);

  const targetMime = useMemo(() => {
    if (outputType) return outputType;
    // Default to original's type when possible
    if (originalFile?.type)
      return originalFile.type as 'image/jpeg' | 'image/png' | 'image/webp';
    return 'image/png';
  }, [originalFile, outputType]);

  const handleCancel = useCallback(() => {
    cleanup();
    // Don't propagate cancel as an error or null; keep existing flows unchanged
  }, [cleanup]);

  const handleApply = useCallback(async () => {
    if (!imageURL || !originalFile || !croppedAreaPixels) return;

    try {
      const blob = await getCroppedBlob(
        imageURL,
        croppedAreaPixels,
        targetMime,
        quality
      );

      if (!blob) {
        onError?.('Failed to crop image.');
        return;
      }

      if (blob.size > maxSize) {
        onError?.('Cropped image exceeds the maximum size.');
        return;
      }

      const croppedName = appendSuffix(
        originalFile.name,
        '-cropped',
        mimeExtension(targetMime)
      );
      const file = new File([blob], croppedName, { type: targetMime });

      onChange(file);
    } catch (e) {
      onError?.('An unexpected error occurred while cropping.');
    } finally {
      cleanup();
    }
  }, [
    cleanup,
    croppedAreaPixels,
    imageURL,
    maxSize,
    onChange,
    onError,
    originalFile,
    quality,
    targetMime,
  ]);

  return (
    <>
      {/* Keep the original common uploader intact. We only intercept onChange. */}
      <ImageUploader
        onChange={handlePreSelect}
        onError={onError}
        accept={accept}
        maxSize={maxSize}
        multiple={multiple}
        disabled={disabled}
      >
        {children as (args: any) => ReactNode}
      </ImageUploader>

      {/* Minimal, dependency-free modal shell styled via Tailwind */}
      {isCropping && imageURL && (
        <Modal
          isOpen={open}
          onClose={handleCancel}
          size="lg"
          customSize={'640px'}
        >
          <div className="w-full rounded-[20px] p-6 sm:w-[640px]">
            <div className="mb-5 flex items-center justify-between">
              <Title as="h4">{modalTitle}</Title>
              <ActionIcon size="sm" variant="text" onClick={handleCancel}>
                <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
              </ActionIcon>
            </div>

            <div className="relative h-[500px] w-full">
              <Cropper
                image={imageURL}
                crop={crop}
                zoom={zoom}
                aspect={aspect}
                cropShape={cropShape}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
                restrictPosition
              />
            </div>

            <div className="flex items-center justify-between gap-4 border-t px-5 py-4">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">Zoom</span>
                <div className="cropZoomRange relative">
                  <input
                    type="range"
                    min={zoomRange[0]}
                    max={zoomRange[1]}
                    step={0.01}
                    value={zoom}
                    onChange={(e) => setZoom(Number(e.target.value))}
                    className="h-3 w-48 cursor-pointer appearance-none rounded-full bg-transparent focus:outline-none"
                    style={{
                      ['--percent' as any]: `${((zoom - zoomRange[0]) / (zoomRange[1] - zoomRange[0])) * 100}%`,
                      ['--brand' as any]: '#0D1321',
                    }}
                  />
                  <style jsx>{`
                    .cropZoomRange
                      input[type='range']::-webkit-slider-runnable-track {
                      height: 8px;
                      border-radius: 9999px;
                      background: linear-gradient(
                        to right,
                        var(--brand) 0%,
                        var(--brand) var(--percent),
                        #e5e7eb var(--percent),
                        #e5e7eb 100%
                      );
                    }
                    .cropZoomRange input[type='range']::-webkit-slider-thumb {
                      -webkit-appearance: none;
                      appearance: none;
                      height: 18px;
                      width: 18px;
                      border-radius: 9999px;
                      background: var(--brand);
                      border: 3px solid #fff;
                      margin-top: -5px;
                      box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
                    }
                    .cropZoomRange input[type='range']::-moz-range-track {
                      height: 8px;
                      border-radius: 9999px;
                      background: linear-gradient(
                        to right,
                        var(--brand) 0%,
                        var(--brand) var(--percent),
                        #e5e7eb var(--percent),
                        #e5e7eb 100%
                      );
                    }
                    .cropZoomRange input[type='range']::-moz-range-thumb {
                      height: 18px;
                      width: 18px;
                      border-radius: 9999px;
                      background: var(--brand);
                      border: 3px solid #fff;
                      box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
                    }
                    .cropZoomRange
                      input[type='range']:focus-visible::-webkit-slider-thumb {
                      outline: 3px solid rgba(13, 19, 33, 0.35);
                      outline-offset: 2px;
                    }
                    .cropZoomRange
                      input[type='range']:focus-visible::-moz-range-thumb {
                      outline: 3px solid rgba(13, 19, 33, 0.35);
                      outline-offset: 2px;
                    }
                  `}</style>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <Button onClick={handleCancel} variant="outline">
                  Cancel
                </Button>
                <Button onClick={handleApply} className="text-white">
                  Apply crop
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
}

// ---------- helpers ----------
function appendSuffix(filename: string, suffix: string, forceExt?: string) {
  const dot = filename.lastIndexOf('.');
  if (dot === -1)
    return `${filename}${suffix}${forceExt ? '.' + forceExt : ''}`;
  const name = filename.slice(0, dot);
  const ext = forceExt || filename.slice(dot + 1);
  return `${name}${suffix}.${ext}`;
}

function mimeExtension(mime: string): string | undefined {
  const map: Record<string, string> = {
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp',
  };
  return map[mime];
}

async function getCroppedBlob(
  imageSrc: string,
  crop: Area,
  mime: 'image/jpeg' | 'image/png' | 'image/webp',
  quality = 0.92
): Promise<Blob | null> {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const { width, height, x, y } = crop;

  canvas.width = Math.max(1, Math.round(width));
  canvas.height = Math.max(1, Math.round(height));

  // Draw the relevant slice
  ctx.drawImage(image, x, y, width, height, 0, 0, canvas.width, canvas.height);

  return await new Promise<Blob | null>((resolve) =>
    canvas.toBlob(
      (b) => resolve(b),
      mime,
      // Quality only applies to lossy formats (jpeg/webp)
      quality
    )
  );
}

function createImage(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (e) => reject(e));
    image.setAttribute('crossOrigin', 'anonymous'); // to avoid CORS issues if any
    image.src = url;
  });
}
