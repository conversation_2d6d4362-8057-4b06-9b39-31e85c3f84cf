import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import {
  ApplicationStatus,
  JobCandidateHiredStat,
  JobCandidateInterviewStat,
  JobCandidateOfferStat,
  JobCandidateQueryKeys,
  OrgCandidateAllPositionsParams,
} from './types';

interface CandidateAllOrgPositionsResponse {
  data: any[];
  highestStage: { applicationId: string; status: ApplicationStatus };
  offer: {
    applicationId: string;
    offerStat: JobCandidateOfferStat;
  };
  nextInterview: {
    applicationId: string;
    interviewStat: JobCandidateInterviewStat;
  };
  hired: { applicationId: string; hiredStat: JobCandidateHiredStat };
  meta: {
    total: number;
    limit: number;
    page: number;
  };
}

async function getCandidateAllPositionsByOrg(
  params: OrgCandidateAllPositionsParams
): Promise<CandidateAllOrgPositionsResponse> {
  const reps = await requestGet<CandidateAllOrgPositionsResponse>(
    API_ENDPONTS.GET_CANDIDATE_ALL_POSITIONS_BY_ORG,
    {},
    cleanQueryParams(params)
  );
  // const reps = await axiosInstance.get<CandidateAllOrgPositionsResponse>(
  //   API_ENDPONTS.GET_CANDIDATE_ALL_POSITIONS_BY_ORG.replace(
  //     ':orgId',
  //     params.orgId || ''
  //   ),
  //   {
  //     params: cleanQueryParams(params),
  //   }
  // );
  return reps.data;
}

export function useGetCandidateAllPositionsByOrg(
  params: OrgCandidateAllPositionsParams
) {
  return useQuery<CandidateAllOrgPositionsResponse>({
    queryKey: [
      JobCandidateQueryKeys.GET_CANDIDATE_ALL_POSITIONS_BY_ORG,
      params,
    ],
    queryFn: () => getCandidateAllPositionsByOrg(params),
    enabled: !!params.orgId && !!params.candidateId,
  });
}
