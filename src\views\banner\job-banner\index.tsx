'use client';

import dynamic from 'next/dynamic';
import HeroTrial from './hero-trial';
import { usePathname } from 'next/navigation';
import JobSuggestion from './job-suggestion';
import { useRef } from 'react';
import cn from '@/utils/class-names';
import JobSearch from './job-search';
import JobFilterV2 from './job-filter-v2';

export default function JobBanner() {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  const searchRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;
  const locationRef = useRef<HTMLInputElement>(
    null
  ) as React.RefObject<HTMLInputElement>;

  return (
    <>
      {isHomePage && (
        <section
          className={cn(
            'relative bg-cover bg-center bg-no-repeat px-4 xl:px-0',
            isHomePage ? 'py-14 xl:py-24' : 'pb-6 pt-14 xl:pt-24'
          )}
          style={{ backgroundImage: 'url("/job/fast-feedback.png")' }}
        >
          <div className="mx-auto flex max-w-[1440px] flex-col gap-6">
            <HeroTrial />
          </div>
        </section>
      )}

      <div
        className={cn(
          'relative w-full',
          isHomePage
            ? 'bg-cover bg-bottom bg-no-repeat py-14 xl:py-24'
            : 'pb-6 pt-14 xl:pt-24'
        )}
        {...(isHomePage
          ? { style: { backgroundImage: `url("/job/job-hero.jpg")` } }
          : {})}
      >
        <div className="mx-auto flex max-w-[1440px] flex-col gap-6 px-4 xl:px-0">
          {!isHomePage && (
            <div>
              <h1 className="mb-2 text-3xl font-bold text-primary md:text-4xl">
                Search Jobs
              </h1>
              <p className="mb mx-auto text-base text-gray-600 md:text-lg">
                Find roles that match your skills, preferences and salary goals.
              </p>
            </div>
          )}

          <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
            <JobSearch searchRef={searchRef} locationRef={locationRef} />

            <JobFilterV2
              isHomePage={isHomePage}
              searchRef={searchRef}
              locationRef={locationRef}
            />
          </div>

          {isHomePage && <JobSuggestion />}
        </div>
      </div>
    </>
  );
}
