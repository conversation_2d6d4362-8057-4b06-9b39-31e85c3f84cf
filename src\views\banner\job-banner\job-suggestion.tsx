'use client';

import { Job, useListJob } from '@/api-requests/job';
import JobCard from './job-card';
import Loading from './loading';
import { useListJobSuggestions } from '@/api-requests/job/get-job-suggestions';
import { Button } from 'rizzui/button';
import { useRouter } from 'next/navigation';
import { useAtom } from 'jotai';
import { userAtom } from '@/store/user-atom';

interface JobCardProps {
  job: Job;
  setSelectedJob: (job: Job) => void;
  selectedJob?: Job | null;
}

export default function JobSuggestion() {
  const router = useRouter();
  const [user] = useAtom(userAtom);

  const { data: jobSuggestions, isLoading } = useListJobSuggestions({
    limit: 4,
    page: 0,
  });

  return (
    <div className="z-10 mx-auto w-full max-w-[1440px] space-y-3 px-4 xl:px-0">
      <div className="flex items-center justify-between">
        <div className="text-xl font-bold">Job suggestions for you</div>
        {user?.id && (
          <Button
            size="sm"
            variant="outline"
            className="border-primary text-primary hover:bg-primary hover:text-white"
            onClick={() => router.push(`profile/${user.id}#applications`)}
          >
            My Applications
          </Button>
        )}
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
        {isLoading ? (
          <Loading />
        ) : (
          jobSuggestions?.map((job, index) => <JobCard key={index} job={job} />)
        )}
      </div>
    </div>
  );
}
