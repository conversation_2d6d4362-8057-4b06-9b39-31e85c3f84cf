'use client';

import {
  ProcessCVToSuggestJobResponse,
  useProcessCVToSuggestJobs,
} from '@/api-requests/job/process-cv-to-suggest-jobs';
import cn from '@/utils/class-names';
import CloseIcon from '@/views/icons/close';
import ImageUploader from '@/views/image-uploader';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { ActionIcon, Button, Loader, Modal, Title } from 'rizzui';

interface IProps {
  open: boolean;
  onClose: () => void;
}

export default function UploadCvModal({ open, onClose }: IProps) {
  const [displayMode, _setDisplayMode] = useState<'form' | 'result'>('form');

  const renderContent = useCallback(() => {
    if (displayMode === 'form') {
      return <TrialFromCV />;
    } else {
      return <TrialResult />;
    }
  }, [displayMode]);

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      // size="lg"
      customSize="800px"
    >
      <div className="w-full rounded-[20px] p-6">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">
            {displayMode === 'form' ? 'Upload CV' : 'Your trial result'}
          </Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        {renderContent()}
      </div>
    </Modal>
  );
}

interface TrialFromCVProps {
  onStart: (t: any) => void;
}
function TrialFromCV() {
  const [fileName, setFileName] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const router = useRouter();

  const handleStart = (trail: ProcessCVToSuggestJobResponse) => {
    if (!trail.jobId) return;
    router.push(`/find-jobs?id=${trail.jobId}`);
    // window.open(
    //   `${API_DOMAINS.AGENTOS_CLOUD}/job-simulation/${trail.simulation.id}?trail=true`,
    //   '_blank'
    // );
  };

  const {
    mutateAsync: processCV,
    isPending,
    data: suggestedTrails,
  } = useProcessCVToSuggestJobs();

  const handleUploadCVToGetSuggestJobs = async (file: File | null) => {
    if (!file || isPending) return;
    try {
      await processCV({ cvFile: file });
    } catch (error) {
      console.log('processCV ::: error :::', error);
    }
  };

  const trials = [
    {
      title: 'Data Analyst — 6 minutes',
      subtitle: 'SQL + chart from sales CSV • Predicted 70-84%',
    },
    {
      title: 'QA Tester — 7 minutes',
      subtitle: 'Write 2 bug reports from a short app demo • Predicted 65-78%',
    },
    {
      title: 'BI Intern — 5 minutes',
      subtitle:
        'Create a simple KPI insight in Power BI mock • Predicted 70-82%',
    },
  ];

  return (
    <div className="w-full">
      {/* Heading */}
      <div className="font-semibold tracking-tight text-slate-700">
        We'll suggest some roles you can try out, with optional proof
        attachments.
      </div>

      <ImageUploader
        onChange={(file: File | null) => {
          setFile(file);
          setFileName(file?.name || null);
          handleUploadCVToGetSuggestJobs(file);
        }}
        accept="application/pdf"
        disabled={isPending}
      >
        {({ getRootProps }) => (
          <div
            {...getRootProps()}
            className={cn(
              'mt-5 rounded-3xl border-2 border-dashed bg-white',
              'flex h-48 w-full items-center justify-center text-slate-400 sm:h-56',
              'transition-colors',
              'border-slate-300'
            )}
          >
            <div className="text-center">
              <p className="text-lg sm:text-xl">
                {fileName ? (
                  <span className="text-slate-600">
                    Selected: <span className="font-medium">{fileName}</span>
                  </span>
                ) : (
                  'Drop your PDF here'
                )}
              </p>
              <p className="mt-1 text-sm text-slate-500">
                Click to choose a file or drag &amp; drop
              </p>
            </div>
          </div>
        )}
      </ImageUploader>

      {/* Suggested trials */}
      <div className="mt-6">
        <div className="font-semibold text-slate-700">Suggested roles</div>
        {!isPending && !suggestedTrails?.length && (
          <div className="mt-3 text-sm text-slate-500">
            Upload your CV to see suggested roles.
          </div>
        )}
        {isPending && (
          <div className="mt-3 flex w-full items-center justify-center">
            <Loader />
          </div>
        )}
        {!!suggestedTrails?.length && (
          <ul className="mt-3 space-y-4">
            {suggestedTrails.map((t, idx) => (
              <li key={t._id || `${idx}`}>
                <div className="flex items-center justify-between rounded-2xl border border-slate-200 bg-white px-4 py-2">
                  <div className="min-w-0 pr-4">
                    <div className="font-semibold">{t.title}</div>
                    <div className="text-sm text-slate-500">
                      {t.simulation?.name} • Expected Fit {t.matchPercentage}%
                    </div>
                  </div>

                  <Button
                    size="sm"
                    rounded="lg"
                    className="min-w-[100px] bg-primary text-white"
                    onClick={() => handleStart(t)}
                  >
                    Apply
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}

type TrialResultProps = {
  score?: number;
  onShare?: () => void;
  onBook?: () => void;
};

function TrialResult({ score = 78 }: TrialResultProps) {
  const safeScore = Math.max(0, Math.min(100, score));

  return (
    <div className="w-full">
      <h2 className="mb-4 font-semibold tracking-tight">Junior Data Analyst</h2>
      <div className="grid grid-cols-1 items-start gap-4 lg:grid-cols-2">
        <div>
          <div className="text-sm text-slate-500">Your score</div>

          <div className="mt-1 text-4xl font-semibold text-slate-900 md:text-5xl">
            {safeScore}%
          </div>

          <div className="mt-3 h-2.5 rounded-full bg-slate-200">
            <div
              className="h-2.5 rounded-full"
              style={{
                width: `${safeScore}%`,
                backgroundColor: '#94A3B8' /* slate-400 */,
              }}
            />
          </div>

          <ul className="mt-5 text-sm leading-6 text-slate-700">
            {[
              'Your strengths look clear and confident.',
              'You may improve with one small revision pass.',
            ].map((b, i) => (
              <li key={i} className="flex">
                <span className="mr-2 select-none">•</span>
                <span>{b}</span>
              </li>
            ))}
          </ul>

          <p className="mt-2 text-sm leading-6 text-slate-600">
            You unlock a recruiter message and feel ready to share your proof.
          </p>
        </div>

        <div className="w-full">
          <div className="flex aspect-[4/3] w-full items-center justify-center rounded-3xl border-2 border-dashed border-slate-300 bg-white">
            <span className="text-base text-slate-400 sm:text-lg">
              Annotated task preview
            </span>
          </div>
        </div>
      </div>

      <div className="mt-6 flex gap-4">
        <Button
          // onClick={onShare}
          className="w-full bg-primary text-white hover:bg-[#0B1E2D]"
          rounded="lg"
        >
          {/* shield icon nhỏ */}
          <svg
            viewBox="0 0 24 24"
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M12 3l7 3v6c0 5-3.5 8-7 9-3.5-1-7-4-7-9V6l7-3z" />
            <path d="M9 12l2 2 4-4" />
          </svg>
          <span className="text-white/90">I share with a hiring manager</span>
        </Button>

        <Button
          // onClick={onBook}
          variant="outline"
          className="w-full border-primary text-primary hover:bg-primary hover:text-white"
          rounded="lg"
        >
          I book a 10-minute coaching call
        </Button>
      </div>
    </div>
  );
}
