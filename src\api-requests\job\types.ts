import { Interaction } from '../interaction';
import { Organization } from '../organization';

export enum JobQueryKeys {
  LIST_JOBS = 'listJobs',
  PROCESS_JOB_SIMULATION = 'processJobSimulation',
  CREATE_JOB = 'createJob',
  APPLY_SIMULATION = 'applySimulation',
  GET_BY_JOBID = 'getJobById',
  UPDATE_JOB = 'updateJob',
  DELETE_JOB = 'deleteJob',
  GET_ORG_HOME_JOBS = 'getOrgHomeJobs',
  GET_ORG_FEATURED_JOBS = 'getOrgFeaturedJobs',
  CHECK_APPLIED = 'checkApplied',
  GET_ORG_PUBLIC_JOB = 'getOrgPublicJob',
  FAVORITE_JOB = 'favoriteJob',
  UNFAVORITE_JOB = 'unfavoriteJob',
  PUBLISH_JOB = 'publishJob',
  UNPUBLISH_JOB = 'unpublishJob',
  CLOSE_JOB = 'closeJob',
  GET_JOB_SUGGESTIONS = 'getJobSuggestions',
}

export const LIMIT = 9;

export const ORG_JOB_LIMIT = 10;

export interface JobListResponse {
  data: Job[];
  meta: {
    total: number;
    limit: number;
    offset: number;
  };
}

export interface JobListParams {
  limit?: number;
  page?: number;
  title?: string;
  location?: string;
  categories?: string;
  jobType?: string;
  salary?: string;
  level?: string;
  id?: string;
}
export interface OrgJobListParams {
  limit?: number;
  page?: number;
  title?: string;
  userId?: string;
  orgId: string;
  status?: string;
  location?: string;
  jobType?: string;
  simLevel?: string;
  applicants?: number;
  created?: number;
  lastAppliedAt?: number;
}

export interface JobUpdateParams {
  jobId: string;
  simulationId: string;
  level: number;
  minute: number;
}

export interface Simulation {
  id: string;
  minute?: number;
  level?: number;
  name?: string;
  description?: string;
  tasks?: any[];
}

export interface Salary {
  min?: number;
  max?: number;
  currency?: string;
  period?: string;
  type?: string;
}

export enum QuestionType {
  YES_NO = 'yes_no',
  SINGLE_CHOICE = 'single',
  MULTI_CHOICE = 'multiple',
  NUMBER = 'number',
  SHORT_TEXT = 'short_text',
  LONG_TEXT = 'long_text',
}

export enum JobStatus {
  PUBLISHED = 'published',
  UNPUBLISHED = 'unpublished',
  DRAFT = 'draft',
  CLOSED = 'closed',
}

interface QuestionOption {
  id: string;
  value: string;
}

export interface JobQuickQuestion {
  id: string;
  text: string;
  type: QuestionType;
  required: boolean;
  options?: QuestionOption[];
  minLength?: number;
  maxLength?: number;
}

export interface Job {
  _id: string;
  jobId: string;
  jobUrl: string;
  isVerified: number;
  title: string;
  description?: string;
  applicants: number;
  benefits: string[];
  postedTimeHuman: string;
  postedTime: Date;
  isFullTime: boolean;
  isHybrid: boolean;
  isOnSite: boolean;
  isRemote: boolean;
  isPartTime: boolean;
  hasContract: boolean;
  levels?: string[];
  salary?: Salary | string;
  location: string;
  country: string;
  countryCode: string;
  region: string;
  city?: string;
  address?: string;
  skills: string[];
  skillsList: string[];
  categories?: string[];
  companyLogoUrl: string;
  companyUrl: string;
  companyName: string;
  isExpired: boolean;
  updatedAt: Date;
  _createdAt: Date;
  totalViews: number;
  totalApplications: number;
  simulation?: Simulation;
  expiresAt?: Date | null;
  isPublished?: boolean;
  applyMode?: string | string[];
  orgId?: string;
  applicationLimit?: number | null;
  interaction?: Interaction;
  // TODO: create new type
  quickQuestions?: JobQuickQuestion[];
  timezone?: string;
  status: JobStatus;
  lastAppliedAt?: Date | null;
  workTypes?: string[];
  workPlaces?: string[];
  favorites?: number;
  expectedFit?: number;
}

export interface PublicJobDetail extends Job {
  org: Organization;
  progress: {
    status: 'string';
  };
}

export interface GetOrgHomeJobsResponse {
  data: any[];
  meta: { lastId: string; hasMore: boolean };
}
