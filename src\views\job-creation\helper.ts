export function parseApplyMode(input: string): string[] {
  switch (input?.toLowerCase()) {
    case 'all':
      return ['simulation', 'cv'];
    case 'simulation':
      return ['simulation'];
    case 'cv':
      return ['cv'];
    case 'disabled':
      return [];
    default:
      return [];
  }
}

export const parseWorkTypeToString = (
  workTypes: string[] | undefined,
  options?: { fallback: string }
): string => {
  const { fallback = '-' } = options || {};
  if (!workTypes || workTypes.length === 0) return fallback;

  const typeMap: Record<string, string> = {
    contract: 'Contract',
    full_time: 'Full-time',
    part_time: 'Part-time',
  };

  return workTypes
    .map(
      (type) =>
        typeMap[type] ||
        type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ')
    )
    .join(', ');
};

export const parseWorkPlaceToString = (
  workPlaces: string[] | undefined,
  options?: { fallback: string }
): string => {
  const { fallback = '-' } = options || {};
  if (!workPlaces || workPlaces.length === 0) return fallback;

  const placeMap: Record<string, string> = {
    remote: 'Remote',
    onsite: 'Onsite',
    hybrid: 'Hybrid',
  };

  return workPlaces
    .map(
      (place) =>
        placeMap[place] ||
        place.charAt(0).toUpperCase() + place.slice(1).replace(/_/g, ' ')
    )
    .join(', ');
};
