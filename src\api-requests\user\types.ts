import { UserProfile } from '../user-profile';

export enum UserQueryKeys {
  GET_USER_TALENTS = 'getUserTalents',
}

export interface UserTalentParams {
  limit?: number;
  page?: number;
  levels?: string[];
  skills?: string[];
  industries?: string[];
  workTypes?: string[];
  workPlaces?: string[];
  search?: string;
  experience?: {
    min?: number;
    max?: number;
  }
  expectedSalary?: {
    min?: number;
    max?: number;
  }
  country?: string;
  jobId?: string;
}

export enum Role {
  USER = 'user',
  EMPLOYER = 'employer',
  EDUCATION = 'education',
  COMMUNITY = 'community',
  ADMIN = 'admin',
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: Role;
  avatar?: string;
  profile?: UserProfile;
}
