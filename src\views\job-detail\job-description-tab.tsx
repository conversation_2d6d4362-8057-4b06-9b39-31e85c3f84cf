'use client';

import Image from 'next/image';
import { Job, JobQuickQuestion } from '@/api-requests/job';
import { safeFormatDate } from '@/utils/date';
import {
  parseWorkPlaceToString,
  parseWorkTypeToString,
} from '../job-creation/helper';
import { useEffect, useState } from 'react';
import { useUpdateApplyMode } from '@/api-requests/job/update-apply-mode';
import { useAtom } from 'jotai';
import { orgAtom } from '@/store/organization-atom';
import toast from 'react-hot-toast';
import { Switch } from 'rizzui';
import { useGetCandidateStatsByJob } from '@/api-requests/job-candidate/get-candidate-stats-by-job';
import QuickQuestion from './quick-question';
interface IProps {
  job: Job;
  jobId?: string;
}

export default function JobDescriptionTab({ job, jobId }: IProps) {
  const [org] = useAtom(orgAtom);

  const [cvSwitch, setCvSwitch] = useState(false);
  const [simulationSwitch, setSimulationSwitch] = useState(false);

  const { mutateAsync, isPending } = useUpdateApplyMode(jobId as string);
  const { data: simulationStats } = useGetCandidateStatsByJob(jobId as string);

  useEffect(() => {
    if (job?.applyMode) {
      setCvSwitch(['all', 'cv'].includes(job?.applyMode as string));
      setSimulationSwitch(
        ['all', 'simulation'].includes(job?.applyMode as string)
      );
    }
  }, [job?.applyMode]);

  const handleApplyModeChange = async (
    cvEnabled: boolean,
    simulationEnabled: boolean
  ) => {
    const previousCvSwitch = cvSwitch;
    const previousSimulationSwitch = simulationSwitch;

    let applyMode: string[] = [];

    if (cvEnabled && simulationEnabled) {
      applyMode = ['cv', 'simulation'];
    } else if (cvEnabled) {
      applyMode = ['cv'];
    } else if (simulationEnabled) {
      applyMode = ['simulation'];
    }

    try {
      const resp = await mutateAsync({
        applyMode,
        orgId: org?._id as string,
      });

      if (resp && resp.isSuccess !== false) {
        toast.success('Update apply mode successfully');
      } else {
        toast.error('Update apply mode failed');
        setCvSwitch(previousCvSwitch);
        setSimulationSwitch(previousSimulationSwitch);
      }
    } catch (error) {
      setCvSwitch(previousCvSwitch);
      setSimulationSwitch(previousSimulationSwitch);
      toast.error('Update apply mode failed');
    }
  };

  const handleCvChange = (checked: boolean) => {
    if (!org?._id) return;

    setCvSwitch(checked);
    handleApplyModeChange(checked, simulationSwitch);
  };

  const handleSimulationChange = (checked: boolean) => {
    if (!org?._id) return;

    setSimulationSwitch(checked);
    handleApplyModeChange(cvSwitch, checked);
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Applicants</div>
          <div className="text-lg font-bold">{job?.applicants || 0}</div>
        </div>
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Favorites</div>
          <div className="text-lg font-bold">{job?.favorites || 0}</div>
        </div>
      </div>

      <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-4">
        <div>
          <div className="text-lg font-bold">Job Summary</div>
          <div className="text-gray-500">
            Compact overview of the posting and configuration.
          </div>
        </div>

        {/* <div className="flex items-center gap-4">
          <span className="flex items-center gap-1">
            {job?.companyLogoUrl ? (
              <Image
                src={job?.companyLogoUrl}
                alt={job?.companyName || 'Company Logo'}
                width={50}
                height={50}
                className="rounded-full object-contain"
                loader={({ src }) => src}
              />
            ) : (
              <div
                className="rounded-full bg-gray-100"
                style={{ width: '100px', height: '100px' }}
              />
            )}
            <span className="font-bold">{job.companyName}</span>
          </span>
        </div> */}

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Level:
              </span>{' '}
              <span>{job?.levels?.join(', ') || '-'}</span>
            </div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Category:
              </span>{' '}
              <span>
                <span>{job?.categories?.join(', ') || '-'}</span>
              </span>
            </div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Work type:
              </span>{' '}
              <span>{parseWorkTypeToString(job?.workTypes)}</span>
            </div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Work place:
              </span>{' '}
              <span>{parseWorkPlaceToString(job?.workPlaces)}</span>
            </div>
          </div>

          <div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Created:
              </span>{' '}
              <span>{safeFormatDate(job?._createdAt, { fallback: '-' })}</span>
            </div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Expires:
              </span>{' '}
              <span>{safeFormatDate(job?.expiresAt, { fallback: '-' })}</span>
            </div>
            <div>
              <span className="inline-block w-[120px] text-gray-500">
                Salary:
              </span>{' '}
              {/* TODO: Format Number */}
              <span>
                {typeof job?.salary === 'string'
                  ? job.salary
                  : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
              </span>
            </div>
          </div>
        </div>

        <div>
          <Switch
            variant="flat"
            label={
              <div>
                <span>Apply via CV</span>
                <span className="pl-1 text-[13px] text-gray-500">
                  ({cvSwitch ? 'Allow ' : 'Not allow '}candidates to apply using
                  their CV)
                </span>
              </div>
            }
            checked={cvSwitch}
            onChange={(e) => handleCvChange(e.target.checked)}
            disabled={isPending || !org?._id} // job?.isPublished ||
          />
          <Switch
            variant="flat"
            label={
              <div>
                <span>Job Simulation</span>
                <span className="pl-1 text-[13px] text-gray-500">
                  ({simulationSwitch ? 'Allow ' : 'Not allow '} candidates to
                  apply by taking a job simulation test)
                </span>
              </div>
            }
            checked={simulationSwitch}
            onChange={(e) => handleSimulationChange(e.target.checked)}
            disabled={isPending || !org?._id} // job?.isPublished ||
          />
        </div>
      </div>

      <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-4">
        <div>
          <div className="text-lg font-semibold">About this role</div>
          <div className="text-gray-500">
            Full job description for internal reference
          </div>
        </div>

        <div>
          <p
            className="text-justify text-sm text-gray-600"
            dangerouslySetInnerHTML={{
              __html: job.description || '',
            }}
          ></p>
        </div>
      </div>

      <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-4">
        <QuickQuestion
          quickQuestions={job?.quickQuestions as JobQuickQuestion[] || []}
        />
      </div>

      <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-4">
        <div>
          <div className="text-lg font-semibold">Simulation</div>
          <div className="text-gray-500">
            Signals from simulation mapped to job skills.
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
          <div className="rounded-2xl border border-[#c3c3c3] p-4">
            <div className="text-gray-500">Avg score</div>
            <div className="text-lg font-bold">
              {simulationStats?.avgScore || 0}
            </div>
          </div>
          <div className="rounded-2xl border border-[#c3c3c3] p-4">
            <div className="text-gray-500">Completion rate</div>
            <div className="text-lg font-bold">
              {simulationStats?.completionRate || 0}
            </div>
          </div>
          <div className="rounded-2xl border border-[#c3c3c3] p-4">
            <div className="text-gray-500">Completed</div>
            <div className="text-lg font-bold">
              {simulationStats?.completed || 0}
            </div>
          </div>
          <div className="rounded-2xl border border-[#c3c3c3] p-4">
            <div className="text-gray-500">Pending</div>
            <div className="text-lg font-bold">
              {simulationStats?.active || 0}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
