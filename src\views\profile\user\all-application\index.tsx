'use client';

import { useState } from 'react';
import Applications from './applications';
import Favourite from '../favourite-jobs/favourite';
import cn from '@/utils/class-names';

const tabs = ['Job Applied', 'Favourite'];

export default function AllApplication() {
  const [activeTab, setActiveTab] = useState(tabs[0]);
  return (
    <div className="rounded-[20px] p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
      {/* <div className="mb-6 flex gap-6">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={cn(
              'font-medium transition-all duration-200',
              activeTab === tab
                ? 'relative text-primary after:block after:h-[2px] after:w-full after:bg-primary'
                : 'text-gray-400 hover:text-gray-600'
            )}
          >
            {tab}
          </button>
        ))}
      </div> */}
      {/* <div>{activeTab === tabs[0] ? <JobApplied /> : <Favourite />}</div> */}
      <div>
        <Applications />
      </div>
    </div>
  );
}
