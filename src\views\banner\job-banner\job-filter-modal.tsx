'use client';

import { SelectOption } from '@/api-requests/types';
import {
  categoriesAtom,
  currentPageAtom,
  workType<PERSON>tom,
  salaryAtom,
  workPlaceAtom,
  levelsAtom,
} from '@/store/job-atom';
import CloseIcon from '@/views/icons/close';
import FieldLabel from '@/views/job-creation/field-label';
import { useAtom } from 'jotai';
import {
  ActionIcon,
  Button,
  Checkbox,
  Input,
  Modal,
  MultiSelect,
  MultiSelectOption,
  Title,
} from 'rizzui';
import { filterOptions } from './job-filter-v2';
import { debounce } from 'lodash';
import { useEffect, useLayoutEffect } from 'react';

function getOptionDisplayValue(option: MultiSelectOption, selected: boolean) {
  return (
    <div
      className="flex w-full cursor-pointer items-center justify-between gap-2 py-1"
      onClick={(e) => {
        e.currentTarget.dispatchEvent(
          new MouseEvent('mousedown', { bubbles: true })
        );
      }}
    >
      <div className="flex items-center gap-1">
        <span className="text-sm">{option.label}</span>
        {option.description && (
          <span className="text-xs text-gray-500">({option.description})</span>
        )}
      </div>
      <Checkbox
        variant="flat"
        size="sm"
        checked={selected}
        onClick={(e) => {
          e.stopPropagation();
          e.currentTarget.dispatchEvent(
            new MouseEvent('mousedown', { bubbles: true })
          );
        }}
        readOnly
      />
    </div>
  );
}

function displayValueCount(title: string, count: number) {
  return (
    <div className="flex w-full items-center truncate text-start">
      <span className="truncate">{title}</span>
      <span className="border-muted ms-2 border-s ps-2">{count} Selected</span>
    </div>
  );
}

interface IProps {
  open: boolean;
  onClose: () => void;
  handleFilterChange: (
    setter: (value: string[]) => void
  ) => (value: string[], paramKey: string) => void;
  minSalaryRef: React.RefObject<HTMLInputElement>;
  maxSalaryRef: React.RefObject<HTMLInputElement>;
  industryRef: React.RefObject<HTMLInputElement>;
  levelRef: React.RefObject<HTMLInputElement>;
  syncUrl: (key: string, value: string[] | string) => void;
  onClear: () => void;
}

export default function JobFilterModal({
  open,
  onClose,
  handleFilterChange,
  minSalaryRef,
  maxSalaryRef,
  industryRef,
  levelRef,
  syncUrl,
  onClear,
}: IProps) {
  const [workTypes, setWorkTypes] = useAtom(workTypeAtom);
  const [salary, setSalary] = useAtom(salaryAtom);
  const [workPlaces, setWorkPlaces] = useAtom(workPlaceAtom);
  const [categories, setCategories] = useAtom(categoriesAtom);
  const [levels, setLevels] = useAtom(levelsAtom);
  const [, setPage] = useAtom(currentPageAtom);

  const handleChangeSalary = debounce(
    (key: 'min' | 'max', e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;

      const currentMin = salary.length >= 1 ? salary[0] : '';
      const currentMax = salary.length >= 2 ? salary[1] : '';

      const minValue = key === 'min' ? val : currentMin;
      const maxValue = key === 'max' ? val : currentMax;

      const newSalary = [minValue, maxValue].filter(Boolean);

      handleFilterChange(setSalary)(newSalary, 'salary');
    },
    500
  );

  // const handleChangeIndustry = debounce(
  //   (e: React.ChangeEvent<HTMLInputElement>) => {
  //     const val = e.target.value;
  //     setCategories(val);
  //     setPage(1);
  //     syncUrl('categories', val);
  //   },
  //   500
  // );

  // const handleChangeLevel = debounce(
  //   (e: React.ChangeEvent<HTMLInputElement>) => {
  //     const val = e.target.value;
  //     setLevels(val);
  //     setPage(1);
  //     syncUrl('levels', val);
  //   },
  //   500
  // );

  return (
    <Modal isOpen={open} onClose={onClose} size="lg" customSize={'450px'}>
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">Filter all</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div className="space-y-4">
            <div>
              <FieldLabel title="Work type" />
              <MultiSelect
                clearable
                value={workTypes}
                options={filterOptions.workType}
                onChange={(val: string[]) =>
                  handleFilterChange(setWorkTypes)(val, 'workType')
                }
                onClear={() => handleFilterChange(setWorkTypes)([], 'workType')}
                getOptionDisplayValue={getOptionDisplayValue}
                displayValue={(selected) =>
                  displayValueCount('Work type', selected.length)
                }
                placeholder="Work type"
                className="w-full rounded-xl"
                selectClassName="rounded-xl"
              />
            </div>

            <div>
              <FieldLabel title="Work place" />
              <MultiSelect
                clearable
                value={workPlaces}
                options={filterOptions.workPlace}
                onChange={(val: string[]) =>
                  handleFilterChange(setWorkPlaces)(val, 'workPlace')
                }
                onClear={() =>
                  handleFilterChange(setWorkPlaces)([], 'workPlace')
                }
                getOptionDisplayValue={getOptionDisplayValue}
                displayValue={(selected) =>
                  displayValueCount('Work place', selected.length)
                }
                placeholder="Work place"
                className="w-full rounded-xl"
                selectClassName="rounded-xl"
              />
            </div>

            <div>
              <FieldLabel title="Industry" />
              <MultiSelect
                clearable
                value={categories}
                options={filterOptions.industry}
                onChange={(val: string[]) =>
                  handleFilterChange(setCategories)(val, 'categories')
                }
                onClear={() =>
                  handleFilterChange(setCategories)([], 'categories')
                }
                getOptionDisplayValue={getOptionDisplayValue}
                displayValue={(selected) =>
                  displayValueCount('Industry', selected.length)
                }
                placeholder="Industry"
                className="w-full rounded-xl"
                selectClassName="rounded-xl"
              />
            </div>

            <div>
              <FieldLabel title="Level" />
              <MultiSelect
                clearable
                value={levels}
                options={filterOptions.level}
                onChange={(val: string[]) =>
                  handleFilterChange(setLevels)(val, 'levels')
                }
                onClear={() => handleFilterChange(setLevels)([], 'levels')}
                getOptionDisplayValue={getOptionDisplayValue}
                displayValue={(selected) =>
                  displayValueCount('Level', selected.length)
                }
                placeholder="Level"
                className="w-full rounded-xl"
                selectClassName="rounded-xl"
              />
            </div>

            <div>
              <FieldLabel title="Salary" />
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <Input
                  ref={minSalaryRef}
                  variant="flat"
                  placeholder="Min"
                  className="w-full"
                  inputClassName="rounded-xl"
                  onChange={(e) => handleChangeSalary('min', e)}
                />
                <Input
                  ref={maxSalaryRef}
                  variant="flat"
                  placeholder="Max"
                  className="w-full"
                  inputClassName="rounded-xl"
                  onChange={(e) => handleChangeSalary('max', e)}
                />
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-4">
            <Button
              variant="outline"
              className="rounded-xl border-primary text-primary hover:bg-primary hover:text-white"
              onClick={onClear}
            >
              Clear all
            </Button>
            <Button
              className="rounded-xl bg-primary text-white"
              onClick={onClose}
            >
              Show Results
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
