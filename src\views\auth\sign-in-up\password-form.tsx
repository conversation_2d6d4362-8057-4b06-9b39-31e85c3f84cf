'use client';

import Field<PERSON>abel from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import { Input, Button, Text, Password } from 'rizzui';
import { ActionType } from './sign-in-modal';
import { useAuthActions } from '@/hooks/use-auth-actions';
import toast from 'react-hot-toast';
import { UserInfo } from '@/store/user-atom';

type FormValues = {
  email: string;
  password: string;
};

interface IProps {
  onSetAction: (action: string) => void;
  onClose: () => void;
  onLoginSuccess?: (resUser?: UserInfo) => void;
}

export default function PasswordForm({
  onSetAction,
  onClose,
  onLoginSuccess,
}: IProps) {
  const { loginPassword } = useAuthActions();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const onSubmit = async (data: FormValues) => {
    const resp = await loginPassword({
      email: data.email,
      password: data.password,
    });

    if (resp?.user) {
      onClose();
      onLoginSuccess?.(resp?.user);
    } else {
      toast.error(resp?.message || 'Login failed');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <div className="font-bold">Welcome to Industry Connect Career</div>
        <div className="text-sm text-[#484848]">
          Welcome back, please enter your account information to continue.
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <FieldLabel title="Email" />
          <Controller
            name="email"
            control={control}
            rules={{
              required: 'Please enter email',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i,
                message: 'Invalid email',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="flat"
                placeholder="<EMAIL>"
                className="w-full"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (isValid) {
                      e.preventDefault();
                      handleSubmit(onSubmit)();
                    }
                  }
                }}
              />
            )}
          />
          {errors.email && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.email.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Password" />
          <Controller
            name="password"
            control={control}
            rules={{
              required: 'Please enter password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your password"
                className="w-full"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (isValid) {
                      e.preventDefault();
                      handleSubmit(onSubmit)();
                    }
                  }
                }}
              />
            )}
          />
          {errors.password && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.password.message}
            </Text>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <Button
          type="submit"
          className="w-full bg-primary text-white"
          disabled={!isValid || isSubmitting}
          onClick={handleSubmit(onSubmit)}
          isLoading={isSubmitting}
        >
          Sign In
        </Button>

        <div className="text-sm">
          <span className="text-[#484848]">Don’t have an account?</span>{' '}
          <button
            className="italic text-primary underline underline-offset-2 hover:opacity-80"
            onClick={() => onSetAction(ActionType.SIGN_UP)}
          >
            Sign Up
          </button>
        </div>
      </div>
    </div>
  );
}
