import React from 'react';

function ManagerIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        d="M10.0023 17.5C6.07234 17.5 4.10817 17.5 2.88734 16.2792C1.6665 15.06 1.6665 13.0958 1.6665 9.16667V6.62C1.6665 5.10667 1.6665 4.35 1.98317 3.78167C2.20912 3.37673 2.54323 3.04261 2.94817 2.81667C3.5165 2.5 4.27317 2.5 5.78817 2.5C6.75817 2.5 7.24317 2.5 7.6665 2.65917C8.63567 3.0225 9.03567 3.90333 9.47317 4.7775L10.0023 5.83333M6.66817 5.83333H13.9615C15.7173 5.83333 16.5948 5.83333 17.2265 6.255C17.4995 6.4372 17.734 6.67138 17.9165 6.94417C18.2448 7.43583 18.3165 8.0775 18.3332 9.16667M14.9998 16.4775C16.4723 16.4775 17.6665 15.2567 17.6665 13.75C17.6665 12.2442 16.4723 11.0225 14.9998 11.0225M14.9998 16.4775C13.5273 16.4775 12.3332 15.2567 12.3332 13.75C12.3332 12.2442 13.5273 11.0225 14.9998 11.0225M14.9998 16.4775V17.5M14.9998 11.0225V10M12.5757 12.2625L11.6673 11.7042M18.3332 15.7967L17.4248 15.2383M17.424 12.2633L18.3323 11.705M11.6665 15.7967L12.5748 15.2383"
        stroke="currentColor"
        strokeLinecap="round"
      />
    </svg>
  );
}

export default ManagerIcon;
