import { ApplicationStatus } from '@/api-requests/job-candidate/types';

const simulationBadgeClasses: Record<string, string> = {
  active: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300',
  completed: 'bg-emerald-100 text-emerald-800 ring-1 ring-emerald-200',
};

const useSimulationStatus = (status?: 'active' | 'completed') => {
  const getSimulationStatusClassName = (status: 'active' | 'completed') => {
    return (
      simulationBadgeClasses[status] ||
      'bg-stone-200 text-stone-800 ring-1 ring-stone-300'
    );
  };

  return {
    classes: status ? getSimulationStatusClassName(status) : '',
    getSimulationStatusClassName,
  };
};

export default useSimulationStatus;
