'use client';

import { UserProfile } from '@/api-requests/user-profile';
import { API_DOMAINS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { atom } from 'jotai';
import { userProfileAtom } from '../user-profile-atom';

export enum Role {
  USER = 'user',
  EMPLOYER = 'employer',
  EDUCATION = 'education',
  COMMUNITY = 'community',
  ADMIN = 'admin',
}

export type UserCVExperience = {
  id: string;
  company: string;
  title: string;
  location?: string;
  startMonth: number;
  startYear: number;
  endMonth?: number;
  endYear?: number;
  description?: string;
};

export type UserCVEducation = {
  id: string;
  school: string;
  field: string;
  degree?: string;
  grade?: string;
  description?: string;
  startMonth?: number;
  startYear?: number;
  endMonth?: number;
  endYear?: number;
};

type UserCVSkills = {
  id: string;
  skill: string;
  level?: number;
};
export type UserCV = {
  id: string;
  about?: string;
  experiences?: UserCVExperience[];
  educations?: UserCVEducation[];
  skills?: UserCVSkills[];
};

export type UserInfo = {
  firstName: string;
  lastName: string;
  userId: string;
  email: string;
  id: string;
  avatar?: string;
  role: Role;
  profile?: {
    // TODO: Temporary profile information
    cv?: UserCV;
    [key: string]: any;
  };
};

export const userAtom = atom<UserInfo | null>(null);
export const userFetchingAtom = atom<boolean>(false);

export const authInitAtom = atom(null, async (_get, set) => {
  try {
    set(userFetchingAtom, true);
    const { data } = await axiosInstance.get('/api/auth/session', {
      baseURL: API_DOMAINS.BASE_URL,
    });
    if (data.user) {
      set(userAtom, data.user);
      set(userProfileAtom, data.profile);
    } else {
      set(userAtom, null);
    }
  } catch {
    set(userAtom, null);
  } finally {
    set(userFetchingAtom, false);
  }
});

export const setUserAtom = atom(
  null,
  async (_get, set, data: { user: UserInfo }) => {
    if (data?.user) {
      set(userAtom, data.user);
      set(userProfileAtom, data.user.profile as UserProfile);
    } else {
      set(userAtom, null);
    }
  }
);

export const updateUserAtom = atom(
  null,
  (
    get,
    set,
    update: Partial<
      Pick<UserInfo, 'firstName' | 'lastName' | 'avatar'> & {
        profile?: { avatar?: string };
      }
    >
  ) => {
    const currentUser = get(userAtom);
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        ...(update.firstName && { firstName: update.firstName }),
        ...(update.lastName && { lastName: update.lastName }),
        ...(update.avatar && { avatar: update.avatar }),
      };
      set(userAtom, updatedUser);
    }
  }
);
