'use client';

import { useGetPublicSimulation } from '@/api-requests/simulation/get-public-simulation';
import cn from '@/utils/class-names';
import { X } from 'lucide-react';
import { Button, Modal } from 'rizzui';

interface IProps {
  open: boolean;
  job: {
    title: string;
  };
  simulationId: string;
  companyName: string;
  companyLogo: string;
  setOpen: (open: boolean) => void;
  onStart: () => void;
}

export default function ApplySimulationModal({
  open,
  simulationId,
  companyName,
  companyLogo,
  job,
  setOpen,
  onStart,
}: IProps) {
  const { data: simulation, isFetched } = useGetPublicSimulation({
    simId: simulationId,
  });
  return (
    <Modal
      isOpen={open}
      onClose={() => setOpen(false)}
      containerClassName="md:w-[800px] md:max-w-[800px]"
      rounded="lg"
    >
      <div>
        {/* Header with branding */}
        <div className="flex items-center justify-between border-b border-neutral-200 px-5 py-4">
          <div className="flex min-w-0 items-center gap-3">
            <img
              src={companyLogo}
              alt={`${companyName}`}
              className="h-8 w-8 flex-shrink-0 rounded-lg object-cover ring-1 ring-neutral-200"
            />
            <div className="min-w-0">
              <h3
                id="simulation-modal-title"
                className="truncate text-base font-semibold tracking-tight"
              >
                Apply with Simulation
              </h3>
              <p className="truncate text-xs text-neutral-500">{companyName}</p>
            </div>
          </div>
          <X
            size={20}
            onClick={() => setOpen(false)}
            className="cursor-pointer"
          />
        </div>

        {/* Content */}
        {simulation && (
          <div className="grid h-full grid-rows-[1fr_auto]">
            <div className="overflow-y-auto px-5 py-5">
              {/* Meta */}
              <div className="mb-4">
                <p className="text-xs font-medium uppercase tracking-wider text-neutral-500">
                  You're applying to
                </p>
                <h4 className="mt-1 break-words text-xl font-semibold leading-tight">
                  {job.title}
                </h4>
              </div>

              {/* Description & Benefits (long-description friendly) */}
              <div className="grid gap-5 md:grid-cols-3">
                <section
                  className={cn('min-w-0 space-y-2 md:col-span-3', {
                    'md:col-span-2': !!simulation.benefits?.length,
                  })}
                >
                  <h5 className="text-sm font-semibold">Description</h5>
                  <p className="break-words text-sm leading-relaxed text-neutral-700">
                    {simulation.description}
                  </p>
                </section>

                {!!simulation.benefits?.length && (
                  <section className="min-w-0 space-y-2 md:col-span-1">
                    <h5 className="text-sm font-semibold">Benefits</h5>
                    <ul className="list-disc space-y-1 break-words pl-5 text-sm text-neutral-700">
                      {simulation.benefits?.map((b, i) => (
                        <li key={i}>
                          {b.charAt(0).toUpperCase() + b.slice(1)}
                        </li>
                      ))}
                    </ul>
                  </section>
                )}
              </div>

              {/* Invitation copy (readable bullets) */}
              <div className="mt-6 rounded-xl border border-neutral-200 bg-neutral-50 p-4">
                <h6 className="mb-2 text-sm font-semibold">
                  What happens next
                </h6>
                <ul className="list-disc space-y-2 pl-5 text-sm text-neutral-800">
                  <li>
                    Start a short, role - specific{' '}
                    <span className="font-medium">Job Simulation</span> to
                    showcase your real skills.
                  </li>
                  <li>
                    Your submissions and scores are sent{' '}
                    <span className="font-medium">directly</span> to the
                    Employer/Recruiter for review.
                  </li>
                  <li>
                    Our system will still{' '}
                    <span className="font-medium">review and validate</span>{' '}
                    your tasks; the employer makes the final shortlist/interview
                    decisions.
                  </li>
                </ul>
              </div>
            </div>

            {/* Footer Actions */}
            <div className="flex flex-col justify-end gap-3 border-t border-neutral-200 px-5 py-4 sm:flex-row">
              <Button
                className="text-white"
                onClick={() => onStart?.()}
                data-autofocus
              >
                Start Simulation to Apply
              </Button>
              <Button
                variant="outline"
                className=""
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
