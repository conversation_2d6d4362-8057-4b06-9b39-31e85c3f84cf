'use client';

import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { SelectOption } from '@/api-requests/types';
import { useGetUserApplications } from '@/api-requests/user-profile/get-user-applications';
import { useWithdrawApplication } from '@/api-requests/user-profile/withdraw-application';
import Pagination from '@/views/pagination';
import Link from 'next/link';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Button, Loader, Select } from 'rizzui';
import ApplicationRow from './application-row';
import ConfirmWithdrawApplicationDialog from './confirm-withdraw-dialog';

const applicationStatusOptions: SelectOption[] = [
  { label: 'Draft', value: ApplicationStatus.DRAFT },
  { label: 'Submitted', value: ApplicationStatus.SUBMITTED },
  { label: 'Under Review', value: ApplicationStatus.UNDER_REVIEW },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  { label: 'Withdrawn', value: ApplicationStatus.WITHDRAWN },
  { label: 'Hired', value: ApplicationStatus.HIRED },
  { label: 'Closed', value: ApplicationStatus.CLOSED },
  // { label: 'Offer Accepted', value: ApplicationStatus.OFFER_ACCEPTED },
  // { label: 'Offer Declined', value: ApplicationStatus.OFFER_DECLINED },
];

const APPLICATION_LIMIT = 10;

export default function Applications() {
  const [page, setPage] = useState(1);
  const [isOpenConfirmWithdrawDialog, setIsOpenConfirmWithdrawDialog] =
    useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<
    string | null
  >(null);
  const [sortAppliedAt, setSortAppliedAt] = useState<'desc' | 'asc'>('desc');
  const [selectedStatus, setSelectedStatus] = useState<SelectOption | null>(
    null
  );

  const {
    data: applications,
    isLoading,
    refetch,
  } = useGetUserApplications({
    page,
    limit: APPLICATION_LIMIT,
    sortAppliedAt,
    status: selectedStatus?.value || '',
  });

  const { mutateAsync: withdrawApplication, isPending } =
    useWithdrawApplication();

  const handleWithdrawApplication = async (id: string) => {
    try {
      await withdrawApplication({ id });
      setIsOpenConfirmWithdrawDialog(false);
      setSelectedApplicationId(null);
      toast.success('Application withdrawn successfully.');
      refetch();
    } catch (error) {
      toast.error('Failed to withdraw application. Please try again later.');
    }
  };

  const handleChangeSort = () => {
    setPage(1);
    setSortAppliedAt((s) => (s === 'desc' ? 'asc' : 'desc'));
  };

  const handleSelectStatus = (option: SelectOption | null) => {
    setPage(1);
    setSelectedStatus(option);
  };

  const handleClickWithdraw = (id: string) => {
    setSelectedApplicationId(id);
    setIsOpenConfirmWithdrawDialog(true);
  };

  return (
    <>
      <div className="mb-6 flex items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            My Applications
          </h1>
          <p className="text-muted-foreground text-sm">
            Track every job you've applied for. Filter by status and sort by
            applied date.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select
            clearable
            options={applicationStatusOptions}
            placeholder="All"
            className="w-full min-w-[220px] sm:w-auto"
            value={selectedStatus}
            onChange={handleSelectStatus}
            prefix="Status: "
            onClear={() => handleSelectStatus(null)}
          />
          <Button
            variant="outline"
            className="gap-2"
            onClick={() => handleChangeSort()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="h-4 w-4"
            >
              <path d="M3 6h13M3 12h9M3 18h5" />
            </svg>
            {sortAppliedAt === 'desc' ? 'Newest first' : 'Oldest first'}
          </Button>
        </div>
      </div>
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex min-h-40 items-center justify-center">
            <Loader className="h-8 w-8" />
          </div>
        ) : (
          applications?.data?.length === 0 && (
            <div>
              <div className="text-center">
                <h3 className="text-lg font-medium">
                  You haven't applied to any jobs yet
                </h3>
                <p className="text-muted-foreground text-sm">
                  Start exploring roles and apply — they'll appear here.
                </p>
                <Button variant="solid" className="mt-3 text-white">
                  <Link href="/find-jobs">Explore jobs</Link>
                </Button>
              </div>
            </div>
          )
        )}

        {(applications?.data || []).map?.((application) => (
          <ApplicationRow
            key={application._id}
            application={application}
            onOpenDetail={() => {}}
            onClickWithdraw={handleClickWithdraw}
          />
        ))}
      </div>

      <div>
        <Pagination
          total={applications?.meta?.total || 0}
          current={page}
          pageSize={APPLICATION_LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          variant="solid"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          // disabled={isLoading}
          hideOnSinglePage
        />
      </div>
      {isOpenConfirmWithdrawDialog && selectedApplicationId && (
        <ConfirmWithdrawApplicationDialog
          open={isOpenConfirmWithdrawDialog}
          setOpen={() => {
            setIsOpenConfirmWithdrawDialog(false);
            setSelectedApplicationId(null);
          }}
          onConfirm={() => {
            if (selectedApplicationId && !isPending) {
              handleWithdrawApplication(selectedApplicationId);
            }
          }}
          isLoading={isPending}
        />
      )}
    </>
  );
}
