import { JobCandidate } from '@/api-requests/job-candidate/types';
import { useState } from 'react';
import { ActionIcon, Button, Input, Modal, Text, Title } from 'rizzui';
import CloseIcon from '../icons/close';
import FieldLabel from '../job-creation/field-label';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';

interface IProps {
  open: boolean;
  onClose: () => void;
  candidate?: ShortlistCandidate | null;
  isLoading?: boolean;
  onCreate: (name: string, candidate?: ShortlistCandidate | null) => void;
}

export default function CreateShortlistModal({
  open,
  onClose,
  candidate,
  isLoading,
  onCreate,
}: IProps) {
  const [name, setName] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleChangeName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (e.target.value.trim() === '') {
      setErrorMessage('Shortlist name is required');
    } else {
      setErrorMessage('');
    }
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full p-6 sm:w-[450px]">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h4">Create New Shortlist</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            <FieldLabel title="Shortlist name" />
            <Input
              value={name}
              onChange={handleChangeName}
              variant="flat"
              placeholder="Enter your full name"
              className="w-full"
            />
            {errorMessage && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errorMessage}
              </Text>
            )}
          </div>

          <div className="flex justify-end gap-3">
            <Button variant="outline" className="border-primary text-primary" onClick={onClose}>
              Cancel
            </Button>
            <Button
              className="bg-primary text-white"
              isLoading={isLoading}
              disabled={isLoading || name.trim() === ''}
              onClick={() => {
                if (name.trim() === '') {
                  setErrorMessage('Shortlist name is required');
                  return;
                }
                onCreate(name, candidate);
              }}
            >
              Create
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
