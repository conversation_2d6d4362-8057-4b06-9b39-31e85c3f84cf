import { atom } from 'jotai';

export interface JobSearchParams {
  title: string;
  location: string;
}

export interface JobFilterParams {
  workType: string[];
  difficulty: string[];
  salary: string[];
  categories: string[];
  workPlace: string[];
  levels: string[]
}

export const titleAtom = atom<string>('');
export const locationAtom = atom<string>('');

export const workTypeAtom = atom<string[]>([]);
export const workPlaceAtom = atom<string[]>([]);
export const difficultyAtom = atom<string[]>([]);
export const salaryAtom = atom<string[]>([]);
export const categoriesAtom = atom<string[]>([]);
export const levelsAtom = atom<string[]>([]);

export const jobSearchParamsAtom = atom<JobSearchParams>((get) => ({
  title: get(titleAtom),
  location: get(locationAtom),
}));

export const jobFilterParamsAtom = atom<JobFilterParams>((get) => ({
  workType: get(workTypeAtom),
  workPlace: get(workPlaceAtom),
  difficulty: get(difficultyAtom),
  salary: get(salaryAtom),
  categories: get(categoriesAtom),
  levels: get(levelsAtom),
}));

export const resetFiltersAtom = atom(null, (get, set) => {
  set(workTypeAtom, []);
  set(workPlaceAtom, []);
  set(difficultyAtom, []);
  set(salaryAtom, []);
  set(categoriesAtom, []);
  set(categoriesAtom, []);
  set(currentPageAtom, 1);
});

export const resetSearchAtom = atom(null, (get, set) => {
  set(titleAtom, '');
  set(locationAtom, '');
  set(currentPageAtom, 1);
});

export const currentPageAtom = atom<number>(1);
