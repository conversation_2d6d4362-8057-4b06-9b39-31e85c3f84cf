'use client';

import { useGetOrgCandidatesQuickAnalytics } from '@/api-requests/job-candidate/get-candidates-quick-analytics';
import { SelectOption } from '@/api-requests/types';
import { CandidatesQuickAnalyticsSkeleton } from './loading';
import { RefreshCw } from 'lucide-react';
import { Tooltip } from 'rizzui/tooltip';
import { JSX } from 'react';

const formatNumber = (num = 0): string => {
  // Thoudsand: K
  // Million: M
  // Billion: B
  if (num >= 1_000_000_000) {
    return (num / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
  }
  if (num >= 1_000_000) {
    return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (num >= 1_000) {
    return (num / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  return num.toString();
};

interface IProps {
  orgId: string;
  dateRange?: Date[] | null;
  jobId?: string;
  simulationId?: string;
  applyMode?: string;
}

const QuickAnalyticCard = ({
  title,
  content,
}: {
  title: string;
  content: string | JSX.Element;
}) => {
  return (
    <div className="flex flex-1 flex-col justify-between gap-4 rounded-2xl border border-[#c3c3c3] bg-white p-4">
      <p className="text-sm text-gray-500">{title}</p>
      <p className="text-lg font-bold">{content}</p>
    </div>
  );
};

export default function CandidatesQuickAnalytics({
  orgId,
  dateRange,
  jobId,
  applyMode,
}: IProps) {
  const { data, isFetched, isLoading, error, refetch } =
    useGetOrgCandidatesQuickAnalytics({
      orgId,
      jobId,
      applyMode,
      appliedFrom: dateRange?.[0] || undefined,
      appliedTo: dateRange?.[1] || undefined,
    });

  if (!isFetched || isLoading) {
    return <CandidatesQuickAnalyticsSkeleton />;
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 rounded-xl border border-red-200 bg-red-50 p-9 text-sm text-red-500">
        <span>Failed to load analytics.</span>
        <RefreshCw
          className="h-4 w-4 cursor-pointer"
          onClick={() => refetch()}
        />
      </div>
    );
  }

  const items = [
    {
      title: 'Total Candidates',
      content: formatNumber(data?.totalCandidates || 0),
    },
    {
      title: 'New Candidates (7d/30d)',
      content: (
        <Tooltip
          color="invert"
          content={
            <div className="text-left">
              <div>
                <span className="inline-block w-7">7d:</span>{' '}
                {formatNumber(data?.last7DaysTotalCandidates || 0)}
              </div>
              <div>
                <span className="inline-block w-7">30d:</span>{' '}
                {formatNumber(data?.last30DaysTotalCandidates || 0)}
              </div>
            </div>
          }
        >
          <span>{`${formatNumber(data?.last7DaysTotalCandidates || 0)} / ${formatNumber(data?.last30DaysTotalCandidates || 0)}`}</span>
        </Tooltip>
      ),
    },
    {
      title: 'Submitted',
      content: formatNumber(data?.totalSubmitted || 0),
    },
    {
      title: 'Under Review',
      content: formatNumber(data?.totalUnderReview || 0),
    },
    {
      title: 'Interview',
      content: formatNumber(data?.totalInterview || 0),
    },
  ];

  return (
    <div className="w-full">
      <p className="mb-1 text-sm text-gray-500">
        Counts reflect the current filters
      </p>
      <div className="flex w-full flex-row flex-wrap gap-1 md:gap-6">
        {items.map((item: { title: string; content: string | JSX.Element }) => (
          <QuickAnalyticCard key={item.title} {...item} />
        ))}
      </div>
    </div>
  );
}
