/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { data } = await axiosInstance.post(
      API_ENDPONTS.FORGET_PASSWORD_VERIFY,
      body
    );

    return NextResponse.json({ status: data?.updated ?? false });
  } catch (e: any) {
    return NextResponse.json(e?.response?.data ?? { message: 'error' }, {
      status: e?.response?.status ?? 500,
    });
  }
}
