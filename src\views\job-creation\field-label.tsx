'use client';

import { Tooltip } from 'rizzui';
import InfoIcon from '../icons/info';

interface IProps {
  title: string;
  content?: string;
  required?: boolean;
}

export default function FieldLabel({
  title,
  content,
  required = false,
}: IProps) {
  return (
    <label className="mb-1 flex items-center gap-1 text-sm">
      {title}
      {required && (
        <span className="text-lg font-semibold leading-none text-red-500">
          *
        </span>
      )}
      {content && (
        <Tooltip content={content} size="sm" color="invert">
          <InfoIcon className="h-5 w-5 text-gray-400" />
        </Tooltip>
      )}
    </label>
  );
}
