import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

async function setOrgPrimaryCandidatePosition(payload: {
  applicationId: string;
  candidateId: string;
  orgId: string;
}) {
  const response = await axiosInstance.patch(
    API_ENDPONTS.SET_ORG_CANDIDATE_PRIMARY_POSITION.replace(
      ':orgId',
      payload.orgId
    )
      .replace(':candidateId', payload.candidateId)
      .replace(':applicationId', payload.applicationId),
    {}
  );
  return response.data;
}

export const useSetOrgPrimaryCandidatePosition = () => {
  return useMutation({
    mutationFn: (payload: {
      applicationId: string;
      candidateId: string;
      orgId: string;
    }) => setOrgPrimaryCandidatePosition(payload),
    onSuccess: () => {
      toast.success('Primary position updated successfully.');
    },
    onError: () => {
      toast.error('Failed to update primary position.');
    },
  });
};
