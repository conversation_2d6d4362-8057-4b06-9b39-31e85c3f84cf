import { Button, Input, Text } from 'rizzui';
import FieldLabel from '../job-creation/field-label';
import { useState } from 'react';
import { Shortlist } from '@/api-requests/shortlist';

interface IProps {
  isLoading?: boolean;
  onEdit?: (name: string, shortlist: Shortlist) => void;
  shortlist: Shortlist;
}

export default function EditShortlist({
  isLoading,
  onEdit,
  shortlist,
}: IProps) {
  const [name, setName] = useState<string>(shortlist?.name);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleChangeName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (e.target.value.trim() === '') {
      setErrorMessage('Shortlist name is required');
    } else {
      setErrorMessage('');
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <FieldLabel title="Shortlist name" />
        <Input
          value={name}
          onChange={handleChangeName}
          variant="flat"
          placeholder="Enter your full name"
          className="w-full"
        />
        {errorMessage && (
          <Text as="p" className="mt-0.5 text-xs text-red-600">
            {errorMessage}
          </Text>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <Button variant="outline" className="border-primary text-primary">
          Cancel
        </Button>
        <Button
          className="bg-primary text-white"
          isLoading={isLoading}
          disabled={isLoading || name.trim() === ''}
          onClick={() => {
            if (name.trim() === '') {
              setErrorMessage('Shortlist name is required');
              return;
            }
            onEdit?.(name, shortlist);
          }}
        >
          Save
        </Button>
      </div>
    </div>
  );
}
