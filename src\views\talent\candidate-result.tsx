'use client';

import { useListUserTalents, User } from '@/api-requests/user';
import CandidateFilter from './candidate-filter';
import CandidateList from './candidate-list';
import { useState } from 'react';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import { searchTextAtom, selectedJobAtom } from '@/store/talent-atom';
import { useAtom } from 'jotai';
import { Button } from 'rizzui';
import { exportToExcel } from '@/utils/export-to-excel';
import toast from 'react-hot-toast';
import { API_DOMAINS } from '@/config/endpoint';
import {
  formatExperience,
  formatSalary,
  formatWorkType,
} from '../profile/user/profile-detail';

const parseRange = (rangeValue: string) => {
  if (rangeValue.includes('-')) {
    const [min, max] = rangeValue.split('-').map(Number);
    return { min, max };
  } else if (rangeValue.includes('+') || rangeValue === '10') {
    const min = parseInt(rangeValue);
    return { min, max: undefined };
  }
  return null;
};

export default function CandidateResult() {
  const [page, setPage] = useState(1);
  const [skills, setSkills] = useState<{ label: string; value: string }[]>([]);
  const [levels, setLevels] = useState<string[]>([]);
  const [industries, setIndustries] = useState<string[]>([]);
  const [workTypes, setWorkTypes] = useState<string[]>([]);
  const [workPlaces, setWorkPlaces] = useState<string[]>([]);
  const [experience, setExperience] = useState<string[]>([]);
  const [expectedSalary, setExpectedSalary] = useState<{
    min: number;
    max: number;
  } | null>(null);
  const [country, setCountry] = useState<SelectOption | null>(null);

  const [searchText, setSearchText] = useAtom(searchTextAtom);
  const [selectedJob, setSelectedJob] = useAtom(selectedJobAtom);

  const buildQueryParams = () => {
    const params: any = {
      page,
      limit: LIMIT,
    };

    if (skills.length > 0) {
      params.skills = skills.map((s) => s.value);
    }

    if (levels.length > 0) {
      params.levels = levels;
    }

    if (industries.length > 0) {
      params.industries = industries;
    }

    if (workTypes.length > 0) {
      params.workTypes = workTypes;
    }

    if (workPlaces.length > 0) {
      params.workPlaces = workPlaces;
    }

    if (experience.length > 0) {
      const ranges = experience
        .map((exp) => parseRange(exp))
        .filter((r): r is NonNullable<typeof r> => r !== null);
      if (ranges.length > 0) {
        const allMins = ranges.map((r) => r.min);
        const allMaxs = ranges.map((r) => r.max).filter((m) => m !== undefined);

        params.experience = {
          min: Math.min(...allMins),
          max: allMaxs.length > 0 ? Math.max(...allMaxs) : undefined,
        };
      }
    }

    // if (expectedSalary.length > 0) {
    //   const ranges = expectedSalary
    //     .map((salary) => parseRange(salary))
    //     .filter((r): r is NonNullable<typeof r> => r !== null);
    //   if (ranges.length > 0) {
    //     const allMins = ranges.map((r) => r.min);
    //     const allMaxs = ranges.map((r) => r.max).filter((m) => m !== undefined);

    //     params.expectedSalary = {
    //       min: Math.min(...allMins),
    //       max: allMaxs.length > 0 ? Math.max(...allMaxs) : undefined,
    //     };
    //   }
    // }

    if (expectedSalary) {
      params.expectedSalary = expectedSalary;
    }

    if (searchText && searchText.trim() !== '') {
      params.search = searchText;
    }

    if (country) {
      params.country = country.label;
    }

    if (selectedJob) {
      params.jobId = selectedJob.value;
    }

    return params;
  };

  const { data, isLoading } = useListUserTalents(buildQueryParams());

  const handleExportToExcel = () => {
    if (data?.data?.length === 0) {
      toast.error('No data to export');
      return;
    }
    exportToExcel(
      [
        'Name',
        'Email',
        'Location',
        'Link profile',
        'Skill',
        'Industry',
        'Level',
        'Work type',
        'Work place',
        'Expected salary',
        'Experience',
      ],
      data?.data.map((item) => [
        item?.firstName + ' ' + item?.lastName,
        item?.email,
        item?.profile?.location,
        `${API_DOMAINS.BASE_URL}/profile/${item?.id}`,
        item.profile?.skills?.join(', '),
        item.profile?.industries?.join(', '),
        item.profile?.levels?.join(', '),
        formatWorkType(item.profile?.workTypes),
        item.profile?.workPlaces?.join(', '),
        formatSalary(item.profile?.expectedSalary),
        formatExperience(item.profile?.experience),
      ]) as string[][],
      {
        fileName: `talent`,
        noName: 'No',
      }
    );
  };

  const handleClearAll = () => {
    setSkills([]);
    setLevels([]);
    setIndustries([]);
    setWorkTypes([]);
    setWorkPlaces([]);
    setExperience([]);
    setExpectedSalary(null);
    setSearchText('');
    setCountry(null);
    setSelectedJob(null);
    setPage(1);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-12 items-center gap-6">
        <div className="col-span-12 flex justify-between md:col-span-4 lg:col-span-3">
          <div className="">Filter</div>
          <button
            className="text-sm italic text-gray-600 underline underline-offset-2 transition-all duration-200 hover:text-primary hover:no-underline"
            onClick={handleClearAll}
          >
            Clear all
          </button>
        </div>
        <div className="col-span-12 flex items-center justify-between md:col-span-8 lg:col-span-9">
          <div className="text-sm">
            {data?.meta?.total || 0} matching candidates
          </div>
          <button
            className="rounded border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
            onClick={handleExportToExcel}
          >
            Export to Excel
          </button>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Sidebar Filters */}
        <CandidateFilter
          skills={skills}
          setSkills={setSkills}
          levels={levels}
          setLevels={setLevels}
          industries={industries}
          setIndustries={setIndustries}
          workTypes={workTypes}
          setWorkTypes={setWorkTypes}
          workPlaces={workPlaces}
          setWorkPlaces={setWorkPlaces}
          experience={experience}
          setExperience={setExperience}
          expectedSalary={expectedSalary}
          setExpectedSalary={setExpectedSalary}
          country={country}
          setCountry={setCountry}
        />

        {/* Candidate List */}
        <CandidateList
          page={page}
          setPage={setPage}
          talent={data as ApiListResponse<User>}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
