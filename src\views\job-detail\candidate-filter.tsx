'use client';

import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { SelectOption } from '@/api-requests/types';
import SearchIcon from '@/views/icons/search';
import { Input, InputProps, Select, SelectProps } from 'rizzui';

const statusOptions: SelectOption[] = [
  { label: 'Submitted', value: ApplicationStatus.SUBMITTED },
  { label: 'Under Review', value: ApplicationStatus.UNDER_REVIEW },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  { label: 'Withdrawn', value: ApplicationStatus.WITHDRAWN },
  { label: 'Hired', value: ApplicationStatus.HIRED },
  // { label: 'Offer Accepted', value: ApplicationStatus.OFFER_ACCEPTED },
  // { label: 'Offer Declined', value: ApplicationStatus.OFFER_DECLINED },
];

const simulationOptions: SelectOption[] = [
  { label: 'Completed', value: 'completed' },
  { label: 'Active', value: 'active' },
];

interface IProps {
  searchProps?: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps?: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  simulationProps?: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
}

export default function CandidateFilter({
  searchProps,
  statusProps,
  simulationProps,
}: IProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="w-full sm:w-auto">
        <Input
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search candidate name"
          className="w-full min-w-[300px] sm:w-auto"
          inputClassName="rounded-lg"
          {...searchProps}
        />
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <div className="flex flex-wrap items-center gap-3">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <div className="flex flex-wrap gap-3">
            <Select
              clearable
              options={statusOptions}
              placeholder="Select status"
              className="w-full min-w-[130px] sm:w-auto"
              {...statusProps}
            />
          </div>

          <div className="flex flex-wrap gap-3">
            <Select
              clearable
              options={simulationOptions}
              placeholder="Select simulation"
              className="w-full min-w-[130px] sm:w-auto"
              {...simulationProps}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
