'use client';

import { Tab } from 'rizzui';
import CandidateListTab from './candidate-list-tab';
import JobDescriptionTab from './job-description-tab';
import { Job } from '@/api-requests/job';

interface IProps {
  job: Job;
  jobId?: string;
  setCandidateInfo?: (info: { totalCandidates: number; matchedCandidates: number; averageMatch: number }) => void;
}

export default function JobDetailTabs({ job, jobId, setCandidateInfo }: IProps) {
  return (
    <Tab className={'[&_.rizzui-tab-list]:border-none'}>
      <Tab.List>
        <Tab.ListItem>Candidate list</Tab.ListItem>
        <Tab.ListItem>Job Detail</Tab.ListItem>
      </Tab.List>
      <Tab.Panels>
        <Tab.Panel><CandidateListTab job={job} jobId={jobId} setCandidateInfo={setCandidateInfo} /></Tab.Panel>
        <Tab.Panel><JobDescriptionTab job={job} jobId={jobId} /></Tab.Panel>
      </Tab.Panels>
    </Tab>
  );
}
