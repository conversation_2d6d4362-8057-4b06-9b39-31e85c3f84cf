import { ApplicationStatus } from '@/api-requests/job-candidate/types';

const applicationBadgeClasses: Record<string, string> = {
  draft: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300', // for incomplete sumulation
  submitted: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300',
  under_review: 'bg-sky-100 text-sky-800 ring-1 ring-sky-200',
  interview: 'bg-[#BBF7D0] text-[#166534] ring-1 ring-[#22C55E]',
  offer: 'bg-amber-100 text-amber-900 ring-1 ring-amber-200',
  rejected: 'bg-[#FECACA] text-[#7F1D1D] ring-1 ring-[#EF4444]',
  withdrawn: 'bg-zinc-200 text-zinc-800 ring-1 ring-zinc-300',
  hired: 'bg-emerald-100 text-emerald-800 ring-1 ring-emerald-200',
  closed: 'bg-primary text-white-800 ring-1 ring-primary',
};

const simulationBadgeClasses: Record<string, string> = {
  active: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300',
  completed: 'bg-emerald-100 text-emerald-800 ring-1 ring-emerald-200',
};

const upperCaseFirstLetter = (str: string) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const getSimulationStatusClassName = (status: string) => {
  return (
    simulationBadgeClasses[status] ||
    'bg-stone-200 text-stone-800 ring-1 ring-stone-300'
  );
};

const getApplicationStatusClassName = (status: ApplicationStatus) => {
  return (
    applicationBadgeClasses[status] ||
    'bg-stone-200 text-stone-800 ring-1 ring-stone-300'
  );
};

const convertSimulationStatus = (status: string) => {
  return upperCaseFirstLetter(status);
};

const convertApplicationStatus = (status: ApplicationStatus) => {
  let statusStr = status ? status.toString() : '';
  if (status === ApplicationStatus.UNDER_REVIEW) {
    statusStr = 'Under Review';
  }
  return upperCaseFirstLetter(statusStr);
};

export {
  convertApplicationStatus,
  convertSimulationStatus,
  getApplicationStatusClassName,
  getSimulationStatusClassName,
};
