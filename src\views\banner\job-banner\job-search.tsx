'use client';

import { Suspense, useEffect, useRef } from 'react';
import { Input, Button } from 'rizzui';
import SearchIcon from '@/views/icons/search';
import LocationIcon from '@/views/icons/location';
import { titleAtom, locationAtom, currentPageAtom } from '@/store/job-atom';
import { useAtom } from 'jotai';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useURLParamsManager } from '@/hooks/use-update-url-params';
interface IProps {
  searchRef?: React.RefObject<HTMLInputElement>;
  locationRef?: React.RefObject<HTMLInputElement>;
}

function JobSearchContent({ searchRef, locationRef }: IProps) {
  const [, setTitle] = useAtom(titleAtom);
  const [, setLocation] = useAtom(locationAtom);
  const [, setPage] = useAtom(currentPageAtom);

  const router = useRouter();
  const pathname = usePathname();
  const urlSearchParams = useSearchParams();

  const urlManager = useURLParamsManager();

  const handleSearch = () => {
    setTitle(searchRef!.current?.value || '');
    setLocation(locationRef!.current?.value || '');
    setPage(1);

    const searchUrl =
      '/find-jobs?title=' +
      encodeURIComponent(searchRef!.current?.value || '') +
      '&location=' +
      encodeURIComponent(locationRef!.current?.value || '') +
      '&page=1';

    if (pathname !== '/find-jobs') {
      setTimeout(() => {
        router.push(searchUrl);
      }, 50);
    } else {
      router.refresh();

      urlManager.update({
        title: searchRef!.current?.value || '',
        location: locationRef!.current?.value || '',
        page: 1,
      });
    }
  };

  useEffect(() => {
    const title = urlSearchParams.get('title') || '';
    const location = urlSearchParams.get('location') || '';
    searchRef!.current!.value = searchRef!.current!.value || title;
    locationRef!.current!.value = locationRef!.current!.value || location;
    setTitle(title);
    setLocation(location);
  }, [setLocation, setTitle, urlSearchParams]);

  // useEffect(() => {
  //   if (pathname === '/') {
  //     if (title || location) {
  //       router.push('/find-jobs');
  //     }
  //   }
  // }, [pathname, router, title, location]);

  return (
    <div className="mx-auto flex w-full flex-col items-stretch justify-between gap-4 rounded-2xl md:flex-row md:items-center">
      <div className="flex flex-[0.65] items-center space-x-3 pr-0">
        <Input
          ref={searchRef}
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Try 'Data Analyst', 'Software Engineer', etc."
          // className="w-full [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          // inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          className="w-full rounded-xl"
          inputClassName="rounded-xl"
          // value={keywords}
          // onChange={(e) => setKeywords(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSearch();
          }}
        />
      </div>

      <div className="flex flex-[0.35] items-center space-x-3 pl-0 pr-0">
        <Input
          ref={locationRef}
          variant="flat"
          prefix={<LocationIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Location"
          // className="w-full [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          // inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          className="w-full rounded-xl"
          inputClassName="rounded-xl"
          // value={locations}
          // onChange={(e) => setLocations(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSearch();
          }}
        />
      </div>

      <div className="">
        <Button
          size="md"
          className="rounded-xl bg-primary font-semibold text-white"
          onClick={handleSearch}
        >
          Search
        </Button>
      </div>
    </div>
  );
}

export default function JobSearch({ searchRef, locationRef }: IProps) {
  return (
    <Suspense fallback={<></>}>
      <JobSearchContent searchRef={searchRef} locationRef={locationRef} />
    </Suspense>
  );
}
