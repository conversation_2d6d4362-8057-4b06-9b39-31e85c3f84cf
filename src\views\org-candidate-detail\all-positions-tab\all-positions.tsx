'use client';

import { ApiListResponse, LIMIT } from '@/api-requests/types';
import useApplicationStatus from '@/hooks/use-application-status';
import useSimulationStatus from '@/hooks/use-simulation-status';
import {
  convertApplicationStatus,
  convertSimulationStatus,
} from '@/utils/application-simulation-status';
import cn from '@/utils/class-names';
import { safeFormatDate } from '@/utils/date';
import { getJobStatusClasses } from '@/views/all-jobs/table';
import Pagination from '@/views/pagination';
import { FileCheck2 } from 'lucide-react';
import Link from 'next/link';
import { Badge, Button, Loader, Table, Tooltip } from 'rizzui';

const getApplyModeText = (mode: string) => {
  switch (mode) {
    case 'cv':
      return 'CV';
    case 'simulation':
      return 'Simulation';
    default:
      return '';
  }
};

interface IProps {
  allPositionsData?: {
    data: any[];
    meta: { total: number; limit: number; page: number };
  };
  isLoadingPositions: boolean;
  isSettingPrimaryPosition: boolean;
  onSetPrimaryPosition: (applicationId: string) => void;
}

export default function AllPositions({
  allPositionsData,
  isLoadingPositions,
  isSettingPrimaryPosition,
  onSetPrimaryPosition,
}: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const { getSimulationStatusClassName } = useSimulationStatus();

  // const actions = [
  //   {
  //     icon: <></>,
  //     label: 'Add to Shortlist',
  //   },
  //   {
  //     icon: <></>,
  //     label: 'View Detail',
  //   },
  // ];

  return (
    <div>
      <div className="rounded-xl bg-white pb-5">
        <div className="overflow-x-auto">
          <Table>
            <Table.Header className="rounded-t-xl border-b border-[#c3c3c3] !bg-white">
              <Table.Row>
                <Table.Head className="min-w-[250px] lg:w-[20%]">
                  Job
                </Table.Head>
                <Table.Head className="">Match</Table.Head>
                <Table.Head className="w-auto lg:min-w-[10%]">
                  Status
                </Table.Head>
                <Table.Head className="w-auto lg:w-[20%]">Applied</Table.Head>
                {/* <Table.Head className="min-w-[350px] lg:w-auto">
                  AI Review
                </Table.Head> */}
                <Table.Head className="!text-right">Actions</Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {allPositionsData?.data?.map((position) => (
                <Table.Row key={position._id} className="border-[#c3c3c3]">
                  <Table.Cell>
                    <div>
                      <p className="text-sm">
                        <Link
                          href={`/org/admin/candidates/${position._id}#all-positions`}
                          className="hover:underline"
                        >
                          {position.job?.title ||
                            position.simulation?.name ||
                            '--'}
                        </Link>
                      </p>
                      <Badge
                        variant="flat"
                        size="sm"
                        className={cn(
                          getJobStatusClasses(
                            position.job?.status ||
                              position.simulation?.status ||
                              ''
                          )
                        )}
                      >
                        {position.job?.status ||
                          position.simulation?.status ||
                          ''}
                      </Badge>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="text-sm font-semibold text-[#1D2D44]">
                      {position.matchPercentage !== undefined ? (
                        `${position.matchPercentage}%`
                      ) : (
                        <Tooltip
                          color="invert"
                          content="User has not completed the simulation"
                        >
                          <span>--%</span>
                        </Tooltip>
                      )}
                    </span>
                  </Table.Cell>

                  <Table.Cell>
                    <div className="flex flex-wrap gap-2">
                      {!!position.isPrimary && (
                        <Badge variant="solid" size="sm" className="text-white">
                          Primary
                        </Badge>
                      )}
                      <Badge
                        variant="flat"
                        size="sm"
                        className={getApplicationStatusClassName(
                          position.applicationStatus
                        )}
                      >
                        {convertApplicationStatus(position.applicationStatus)}
                      </Badge>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <div className="flex flex-col gap-1">
                      <p className="text-sm">
                        <Tooltip
                          color="invert"
                          content={safeFormatDate(position.appliedAt, {
                            format: 'full',
                          })}
                        >
                          <span className="inline space-x-1">
                            <span>
                              {safeFormatDate(position.appliedAt, {
                                format: 'short',
                              })}
                            </span>
                            <span>•</span>
                            <span>
                              {safeFormatDate(position.appliedAt, {
                                format: 'relative',
                              })}
                            </span>
                          </span>
                        </Tooltip>
                      </p>

                      <p className="flex gap-1 text-sm text-gray-500">
                        Method:{' '}
                        <Badge variant="outline" size="sm">
                          {getApplyModeText(position.applyMode)}
                        </Badge>
                        {(position.applyMode === 'simulation' ||
                          !!position.hasQuickQuestions) && <span>•</span>}
                        {position.applyMode === 'simulation' && (
                          <Badge
                            variant="flat"
                            size="sm"
                            className={getSimulationStatusClassName(
                              position.simulationStatus
                            )}
                          >
                            {convertSimulationStatus(position.simulationStatus)}
                          </Badge>
                        )}
                        {!!position.hasQuickQuestions && (
                          <Badge variant="outline" size="sm">
                            Questions
                          </Badge>
                        )}
                      </p>
                    </div>
                  </Table.Cell>

                  {/* <Table.Cell>
                    <div>
                      <LongTextCell
                        text={position.aiEvaluation?.summary || ''}
                        lines={2}
                      />
                    </div>
                  </Table.Cell> */}

                  <Table.Cell className="text-right">
                    <div>
                      {!position.isPrimary && (
                        <Button
                          variant="solid"
                          className="text-white"
                          size="sm"
                          onClick={() => onSetPrimaryPosition(position._id)}
                          disabled={isSettingPrimaryPosition}
                        >
                          <FileCheck2 className="mr-2 h-4 w-4" /> Set Primary
                        </Button>
                      )}
                    </div>
                    {/* <div className="flex justify-end gap-3">
                      <Dropdown placement="bottom-end">
                        <Dropdown.Trigger>
                          <MoreHorizontalIcon />
                        </Dropdown.Trigger>
                        <Dropdown.Menu className="w-fit divide-y">
                          {actions.map((action, idx) => (
                            <Dropdown.Item
                              key={idx}
                              className="hover:bg-primary hover:text-white"
                              onClick={() =>
                                onClickAction(action.label, position)
                              }
                            >
                              <div className="flex items-center">
                                {action.icon}
                                <span className="ml-2">{action.label}</span>
                              </div>
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div> */}
                  </Table.Cell>
                </Table.Row>
              ))}

              {isLoadingPositions ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="h-40 text-center">
                    <div className="flex min-h-40 items-center justify-center">
                      <Loader className="h-8 w-8" />
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                allPositionsData?.data?.length === 0 && (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center">
                      <div className="text-gray-500">No candidates found</div>
                    </Table.Cell>
                  </Table.Row>
                )
              )}
            </Table.Body>
          </Table>
        </div>

        <hr className="border-t border-[#c3c3c3] pt-4" />

        <Pagination
          total={allPositionsData?.meta?.total || 0}
          current={1}
          pageSize={allPositionsData?.meta?.limit || LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => {
            // setPage(page);
          }}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
          hideOnSinglePage
        />
      </div>
    </div>
  );
}
