'use client';

import ReactQuill from 'react-quill-new';
import { FieldError, Text } from 'rizzui';
import React, { useMemo } from 'react';
import cn from '@/utils/class-names';

interface QuillEditorProps extends ReactQuill.ReactQuillProps {
  error?: string;
  label?: React.ReactNode;
  className?: string;
  labelClassName?: string;
  errorClassName?: string;
  toolbarPosition?: 'top' | 'bottom';
  containerOptions?: unknown[];
  toolbarOptions?: { [key: string]: unknown };
  text?: string;
}

const QuillEditor = React.forwardRef<ReactQuill, QuillEditorProps>(
  (
    {
      label,
      error,
      className,
      labelClassName,
      errorClassName,
      toolbarPosition = 'top',
      containerOptions = [],
      toolbarOptions = {},
      text,
      ...props
    },
    ref
  ) => {
    const quillModules = useMemo(() => {
      return {
        toolbar: {
          container: [
            [{ header: [1, 2, 3, 4, 5, 6, false] }],

            ['bold', 'italic', 'underline', 'strike'], // toggled buttons
            ['blockquote', 'code-block'],

            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
            [{ indent: '-1' }, { indent: '+1' }], // outdent/indent

            [{ color: [] }, { background: [] }], // dropdown with defaults from theme
            [{ font: [] }],
            [{ align: [] }],
            ['link'],
            ...containerOptions,

            ['clean'],
          ],
          ...toolbarOptions,
        },
      };
    }, [containerOptions, toolbarOptions]);

    return (
      <div className={cn(className)}>
        {label && (
          <label className={cn('mb-1.5 block', labelClassName)}>{label}</label>
        )}
        <ReactQuill
          modules={quillModules}
          className={cn(
            'react-quill rounded-xl border [&_*]:!border-[#c3c3c3] [&_.ql-container]:rounded-b-xl [&_.ql-container]:bg-white [&_.ql-toolbar]:rounded-t-xl [&_.ql-toolbar]:border-t [&_.ql-toolbar]:border-t-[#c3c3c3] [&_.ql-toolbar]:bg-white',
            toolbarPosition === 'bottom' &&
              'react-quill-toolbar-bottom relative',
            className
          )}
          ref={ref}
          {...props}
        />
        <div className="flex items-center justify-between">
          {error ? (
            <FieldError size="md" error={error} className={errorClassName} />
          ) : (
            <div></div>
          )}
          {text && <Text className="text-xs text-gray-500">{text}</Text>}
        </div>
      </div>
    );
  }
);

QuillEditor.displayName = 'QuillEditor';

export default QuillEditor;
