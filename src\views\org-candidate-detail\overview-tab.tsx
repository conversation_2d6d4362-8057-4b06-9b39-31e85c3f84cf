import { JobCandidate } from '@/api-requests/job-candidate/types';
import SimpleMarkdown from '@/components/SimpleMarkdown';
import useApplicationStatus from '@/hooks/use-application-status';
import { Section } from '@/shared/section';
import { convertApplicationStatus } from '@/utils/application-simulation-status';
import cn from '@/utils/class-names';
import { ChevronDownIcon, ShieldAlert } from 'lucide-react';
import Link from 'next/link';
import { Accordion, Badge, Tooltip } from 'rizzui';

interface IProps {
  candidate: JobCandidate;
  onOpenAllPositions: () => void;
}

// Star rating component
const StarRating = ({
  rating,
  maxRating = 5,
}: {
  rating: number;
  maxRating?: number;
}) => {
  return (
    <div className="flex items-center gap-1">
      {Array.from({ length: maxRating }, (_, index) => (
        <svg
          key={index}
          className={cn(
            'h-4 w-4 fill-current',
            index < rating ? 'text-primary' : 'text-gray-300'
          )}
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  );
};

// const Rating = ({
//   rating,
//   maxRating = 5,
// }: {
//   rating: number;
//   maxRating?: number;
// }) => {
//   return (
//     <div className="flex flex-wrap items-center gap-2">
//       {Array.from({ length: maxRating }, (_, index) => (
//         <div
//           key={index}
//           className={cn(
//             'h-2 w-16 rounded-full',
//             index < rating ? 'bg-[#353535]' : 'bg-gray-300'
//           )}
//         />
//       ))}
//     </div>
//   );
// };

export default function OverviewTab({ candidate, onOpenAllPositions }: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();

  return (
    <div className="space-y-4">
      {/* Score Cards */}
      <div className={cn('grid grid-cols-1 gap-4 md:grid-cols-4')}>
        {/* Overall Score */}
        {candidate.applyMode === 'simulation' && (
          <Section>
            <div className="mb-2 text-sm text-gray-500">Performance Score</div>
            <div className="text-3xl font-bold text-gray-900">
              {candidate.performanceScore ?? '--'}
            </div>
          </Section>
        )}

        {/* Match */}
        <Section>
          <div className="mb-2 text-sm text-gray-500">Match</div>
          <div className="text-3xl font-bold text-gray-900">
            {candidate.matchPercentage ?? '--'}%
          </div>
        </Section>

        {/* Signals */}
        <div
          className={cn(
            'rounded-2xl border border-[#c3c3c3] bg-white p-6',
            candidate.applyMode === 'cv' ? 'col-span-3' : 'col-span-2'
          )}
        >
          <div className="mb-2 text-sm text-gray-500">Signals</div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-900">
              Risk & Duplicates
            </div>
            <div className="space-y-1">
              {(candidate?.aiEvaluation?.risks || []).map((risk, idx) => (
                <div className="flex items-center gap-2 text-sm" key={idx}>
                  {/* <div className="h-2 w-2 rounded-full bg-orange-400"></div> */}
                  <ShieldAlert
                    className={cn(
                      'h-4 w-4 text-yellow-600',
                      risk.level === 'medium' ? 'text-orange-600' : '',
                      risk.level === 'high' ? 'text-red-600' : ''
                    )}
                  />
                  <Tooltip color="invert" content={risk.mitigation || ''}>
                    <span>
                      {risk.name}: {risk.explanation}
                    </span>
                  </Tooltip>
                </div>
              ))}
            </div>
            <div>
              {!!candidate.otherJobs?.length && (
                <div
                  className="cursor-pointer rounded-xl border p-2 text-xs"
                  onClick={() => {
                    onOpenAllPositions?.();
                  }}
                >
                  <div className="mb-1 cursor-pointer font-bold hover:underline">
                    Possible duplicates:
                  </div>
                  {!!candidate.otherJobs?.length && (
                    <div className="space-y-1">
                      {candidate.otherJobs.map((otherJob) => (
                        <div
                          key={otherJob.id}
                          className="flex items-center justify-between"
                        >
                          <Link
                            href={`/org/admin/candidates/${otherJob.id}`}
                            className="underline-offset-2 hover:underline"
                          >
                            {otherJob.job?.title ||
                              otherJob.simulation?.name ||
                              '--'}
                          </Link>
                          <Badge
                            variant="flat"
                            size="sm"
                            className={getApplicationStatusClassName(
                              otherJob.applicationStatus
                            )}
                          >
                            {convertApplicationStatus(
                              otherJob.applicationStatus
                            )}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
            {/* <div className="mt-3 flex items-center justify-between">
              <span className="text-sm text-gray-500">Possible duplicates</span>
              <Badge variant="outline" className="text-xs">
                pending
              </Badge>
            </div> */}
          </div>
        </div>
      </div>

      {/* Strengths and Weaknesses */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <Section>
          <h3 className="text-lg font-semibold text-gray-900">Strengths</h3>
          <p className="mb-4 text-sm text-gray-500">
            Top signals from Simulation/CV analysis
          </p>
          <ul className="space-y-3">
            {!!candidate.aiEvaluation?.strengths?.length &&
              candidate.aiEvaluation.strengths.map((item, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary"></div>
                  <span className="text-sm text-gray-700">
                    <span className="font-bold">
                      {item.point || item || ''}
                    </span>
                    {!!item.point && !!item.evidence && `: ${item.evidence}`}
                  </span>
                </li>
              ))}
          </ul>
        </Section>

        <Section>
          <h3 className="text-lg font-semibold text-gray-900">Weaknesses</h3>
          <p className="mb-4 text-sm text-gray-500">
            Areas to probe in interview
          </p>
          <ul className="space-y-3">
            {!!candidate.aiEvaluation?.areasForImprovement?.length &&
              candidate.aiEvaluation.areasForImprovement.map((item, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary"></div>
                  <span className="text-sm text-gray-700">
                    <span className="font-bold">
                      {item.point || item || ''}
                    </span>
                    {!!item.point && !!item.evidence && `: ${item.evidence}`}
                  </span>
                </li>
              ))}
          </ul>
        </Section>
      </div>

      {/* Skills Assessment */}
      <Section>
        <h3 className="text-lg font-semibold text-gray-900">
          Skills Assessment
        </h3>
        <p className="mb-6 text-sm text-gray-500">
          Structured competency ratings
        </p>

        <div className="grid grid-cols-1 gap-8 xl:grid-cols-2">
          {/* Hard Skills */}
          <div>
            <h4 className="mb-4 text-sm font-semibold text-gray-900">
              Hard skills
            </h4>
            <div className="space-y-4">
              {!!candidate.aiEvaluation?.skills?.hardSkills?.length &&
                candidate.aiEvaluation.skills.hardSkills.map((skill, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{skill.name}</span>
                    <StarRating rating={skill.rating} />
                  </div>
                ))}
            </div>
          </div>

          {/* Soft Skills */}
          <div>
            <h4 className="mb-4 text-sm font-semibold text-gray-900">
              Soft skills
            </h4>
            <div className="space-y-4">
              {!!candidate.aiEvaluation?.skills?.softSkills?.length &&
                candidate.aiEvaluation.skills.softSkills.map((skill, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{skill.name}</span>
                    <StarRating rating={skill.rating} />
                  </div>
                ))}
            </div>
          </div>
        </div>
      </Section>

      {!!candidate.quickQuestions?.length && (
        <Section>
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Quick Questions
          </h3>
          {/* <p className="mb-6 text-sm text-gray-500">
            Submissions from Simulation
          </p> */}

          <div className="space-y-4 pl-4">
            {candidate.quickQuestions.map((question, idx) => (
              <div key={question.id}>
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    {idx + 1}. {question.text}
                  </h4>
                </div>
                <p className="mb-2 ml-2 whitespace-pre-line text-sm text-gray-600">
                  {question.answer}
                </p>
                {/* {question.submission?.content && (
                  <div className="rounded bg-gray-50 p-3 text-sm text-gray-700">
                    <p className="mb-1 font-medium">Submission:</p>
                    <p className="whitespace-pre-wrap">
                      {question.submission.content}
                    </p>
                  </div>
                )} */}
              </div>
            ))}
          </div>
        </Section>
      )}

      {/* Task Results */}
      {!!candidate.tasks?.length && (
        <Section>
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Task Results
          </h3>

          {!!candidate.tasks?.length && (
            <div className="space-y-6">
              {candidate.tasks.map((task, idx) => (
                <Accordion
                  key={task.id || `${idx}`}
                  className="mx-8 border-b last-of-type:border-b-0"
                >
                  <Accordion.Header>
                    {({ open }) => (
                      <div className="flex w-full cursor-pointer items-center justify-between py-5 text-sm font-semibold">
                        {idx + 1}. {task.title}
                        <ChevronDownIcon
                          className={cn(
                            'h-5 w-5 -rotate-90 transform transition-transform duration-300',
                            open && '-rotate-0'
                          )}
                        />
                      </div>
                    )}
                  </Accordion.Header>
                  <Accordion.Body className="mb-7 ml-4">
                    <SimpleMarkdown
                      content={task.description}
                      className="text-sm"
                    />
                    {!!task.submissions?.length && (
                      <>
                        <p className="mb-1 font-medium">Submissions:</p>
                        <div className="rounded bg-gray-50 p-3 text-sm text-gray-700">
                          {task.submissions.map((submission, idx) => (
                            <Accordion
                              key={idx}
                              className="mx-8 border-b last-of-type:border-b-0"
                            >
                              <Accordion.Header>
                                {({ open }) => (
                                  <div className="flex w-full cursor-pointer items-center justify-between py-5 text-sm font-semibold">
                                    Submission {idx + 1}:
                                    <ChevronDownIcon
                                      className={cn(
                                        'h-5 w-5 -rotate-90 transform transition-transform duration-300',
                                        open && '-rotate-0'
                                      )}
                                    />
                                  </div>
                                )}
                              </Accordion.Header>
                              <Accordion.Body className="mb-7">
                                {/* <SimpleMarkdown
                                content={submission.content}
                                className="text-sm"
                              /> */}
                                <div
                                  className="whitespace-pre-wrap"
                                  dangerouslySetInnerHTML={{
                                    __html: submission.content,
                                  }}
                                ></div>
                              </Accordion.Body>
                            </Accordion>
                          ))}
                        </div>
                      </>
                    )}
                  </Accordion.Body>
                </Accordion>
              ))}
            </div>
          )}
        </Section>
      )}
    </div>
  );
}
