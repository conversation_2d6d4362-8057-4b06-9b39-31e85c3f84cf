'use client';

import { But<PERSON>, Modal } from 'rizzui';

interface IProps {
  open: boolean;
  currentStatus: string;
  newStatus: string;
  setOpen: (open: boolean) => void;
}

export default function CannotChangeStatusDialog({
  open,
  currentStatus,
  newStatus,
  setOpen,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={() => setOpen(false)}>
      <div className="flex flex-col items-center gap-5 p-6 text-center">
        <p className="text-lg font-bold text-[#484848]">Update Status</p>
        <p className="text-[16px]">
          The status cannot be changed from{' '}
          <span className="font-bold">{currentStatus}</span> to{' '}
          <span className="font-bold">{newStatus}</span>.
        </p>
        <div>
          <Button
            onClick={() => setOpen(false)}
            variant="solid"
            className="text-white"
          >
            <span>Confirm</span>
          </Button>
        </div>
      </div>
    </Modal>
  );
}
