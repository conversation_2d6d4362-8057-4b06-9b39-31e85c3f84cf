'use client';

import { <PERSON><PERSON><PERSON>, Button, Dropdown, Switch } from 'rizzui';
import JobDetailTabs from './job-detail-tabs';
import { useParams, useRouter } from 'next/navigation';
import { useGetJob } from '@/api-requests/job/get-by-id';
import { Job, JobStatus } from '@/api-requests/job';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useUpdateApplyMode } from '@/api-requests/job/update-apply-mode';
import { useAtom } from 'jotai';
import { orgAtom } from '@/store/organization-atom';
import Breadcrumb from '@/shared/breadcrumb';
import cn from '@/utils/class-names';
import { jobStatusClasses } from '../all-jobs/table';
import PageHeader from '@/shared/page-header';
import MoreHorizontalIcon from '../icons/more-horizontal';
import EditIcon from '../icons/edit';
import UnpublishedIcon from '../icons/unpublished';
import PublishedIcon from '../icons/published';
import CloseIcon from '../icons/close';
import ConfirmPublishModal from '../job-creation/confirm-publish-modal';
import ConfirmModal from '@/shared/confirm-modal';
import { usePublishJob } from '@/api-requests/job/publish-job';
import { useUnpublishJob } from '@/api-requests/job/unpublish-job';
import { useCloseJob } from '@/api-requests/job/close-job';

export default function JobDetail() {
  const router = useRouter();
  const { id } = useParams();

  const [org] = useAtom(orgAtom);

  const [cvSwitch, setCvSwitch] = useState(false);
  const [simulationSwitch, setSimulationSwitch] = useState(false);
  const [candidateInfo, setCandidateInfo] = useState({
    totalCandidates: 0,
    matchedCandidates: 0,
    averageMatch: 0,
  });
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openUnpublishModal, setOpenUnpublishModal] = useState(false);
  const [openCloseModal, setOpenCloseModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  const { data: job, isLoading, refetch } = useGetJob(id as string);
  const { mutateAsync, isPending } = useUpdateApplyMode(id as string);

  const { mutateAsync: publishMutateAsync, isPending: isPublishing } =
    usePublishJob();
  const { mutateAsync: unpublishMutateAsync, isPending: isUnpublishing } =
    useUnpublishJob();
  const { mutateAsync: closeMutateAsync, isPending: isClosing } = useCloseJob();

  useEffect(() => {
    if (job?.applyMode) {
      setCvSwitch(['all', 'cv'].includes(job?.applyMode as string));
      setSimulationSwitch(
        ['all', 'simulation'].includes(job?.applyMode as string)
      );
    }
  }, [job?.applyMode]);

  const handleApplyModeChange = async (
    cvEnabled: boolean,
    simulationEnabled: boolean
  ) => {
    const previousCvSwitch = cvSwitch;
    const previousSimulationSwitch = simulationSwitch;

    let applyMode: string[] = [];

    if (cvEnabled && simulationEnabled) {
      applyMode = ['cv', 'simulation'];
    } else if (cvEnabled) {
      applyMode = ['cv'];
    } else if (simulationEnabled) {
      applyMode = ['simulation'];
    }

    try {
      const resp = await mutateAsync({
        applyMode,
        orgId: org?._id as string,
      });

      if (resp && resp.isSuccess !== false) {
        toast.success('Update apply mode successfully');
      } else {
        toast.error('Update apply mode failed');
        setCvSwitch(previousCvSwitch);
        setSimulationSwitch(previousSimulationSwitch);
      }
    } catch (error) {
      setCvSwitch(previousCvSwitch);
      setSimulationSwitch(previousSimulationSwitch);
      toast.error('Update apply mode failed');
    }
  };

  const handleCvChange = (checked: boolean) => {
    if (!org?._id) return;

    setCvSwitch(checked);
    handleApplyModeChange(checked, simulationSwitch);
  };

  const handleSimulationChange = (checked: boolean) => {
    if (!org?._id) return;

    setSimulationSwitch(checked);
    handleApplyModeChange(cvSwitch, checked);
  };

  const handlePublishJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await publishMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job published successfully');
      setOpenPublishModal(false);
    } else {
      toast.error('Failed to publish job');
    }
  };

  const handleUnpublishJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await unpublishMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job unpublished successfully');
      setOpenUnpublishModal(false);
    } else {
      toast.error('Failed to unpublish job');
    }
  };

  const handleCloseJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await closeMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job closed successfully');
      setOpenCloseModal(false);
    } else {
      toast.error('Failed to close job');
    }
  };

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb
          items={[
            { name: 'Jobs', href: '/org/admin/jobs' },
            {
              name: job?.title || 'Job details',
              href: `/org/admin/jobs/${id}`,
            },
          ]}
        />

        {job && (
          <PageHeader
            title={
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold">{job?.title}</div>
                <div
                  className={cn(
                    'rounded-full px-4 py-0.5 text-sm',
                    jobStatusClasses[job?.status as string]
                  )}
                >
                  {(job?.status as string)?.charAt(0).toUpperCase() +
                    job?.status?.slice(1)}
                </div>
              </div>
            }
            description={job?.location}
            action={
              <div className="flex items-center gap-2">
                <Button
                  className="text-white"
                  onClick={() => router.push('/org/admin/candidates')}
                >
                  Manage Candidates
                </Button>
                <Dropdown placement="bottom-end">
                  <Dropdown.Trigger>
                    <div className="flex h-10 w-10 cursor-pointer items-center justify-center rounded-xl border border-[#c3c3c3] hover:border-primary">
                      <MoreHorizontalIcon />
                    </div>
                  </Dropdown.Trigger>
                  <Dropdown.Menu className="w-fit divide-y">
                    {[
                      {
                        icon: <EditIcon className="h-5 w-5" />,
                        label: 'Edit',
                        onClick: (job: Job) =>
                          router.push(`/org/admin/jobs/edit/${job.jobId}`),
                      },
                      ...((job.status === JobStatus.PUBLISHED && [
                        {
                          icon: <UnpublishedIcon className="h-5 w-5" />,
                          label: 'Unpublish Job',
                          onClick: (job: Job) => {
                            setSelectedJob(job);
                            setOpenUnpublishModal(true);
                          },
                        },
                      ]) ||
                        []),
                      ...(([JobStatus.UNPUBLISHED, JobStatus.DRAFT].includes(
                        job.status
                      ) && [
                        {
                          icon: <PublishedIcon className="h-5 w-5" />,
                          label: 'Publish Job',
                          onClick: (job: Job) => {
                            setSelectedJob(job);
                            setOpenPublishModal(true);
                          },
                        },
                      ]) ||
                        []),
                      ...(([JobStatus.PUBLISHED].includes(job.status) && [
                        {
                          icon: <CloseIcon className="h-5 w-5" />,
                          label: 'Close Job',
                          onClick: (job: Job) => {
                            setSelectedJob(job);
                            setOpenCloseModal(true);
                          },
                        },
                      ]) ||
                        []),
                    ].map((action, idx) => (
                      <Dropdown.Item
                        key={idx}
                        className="hover:bg-primary hover:text-white"
                        onClick={() => action.onClick && action.onClick(job)}
                      >
                        <div className="flex items-center">
                          {action.icon}
                          <span className="ml-2">{action.label}</span>
                        </div>
                      </Dropdown.Item>
                    ))}
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            }
          />
        )}

        <JobDetailTabs
          job={job as Job}
          jobId={id as string}
          setCandidateInfo={setCandidateInfo}
        />
      </div>

      {openPublishModal && (
        <ConfirmPublishModal
          open={openPublishModal}
          onClose={() => setOpenPublishModal(false)}
          isLoading={isPublishing}
          onPublish={() => handlePublishJob(selectedJob as Job)}
        />
      )}

      {openUnpublishModal && (
        <ConfirmModal
          open={openUnpublishModal}
          onClose={() => setOpenUnpublishModal(false)}
          isLoading={isUnpublishing}
          title="Confirm Unpublish Job"
          content={
            <div>
              <div className="font-semibold">
                Are you sure you want to unpublish this job?
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  Once unpublished, the job will no longer be visible to users.
                </p>
              </div>
            </div>
          }
          confirmButtonText="Unpublish"
          onConfirm={() => handleUnpublishJob(selectedJob as Job)}
        />
      )}

      {openCloseModal && (
        <ConfirmModal
          open={openCloseModal}
          onClose={() => setOpenCloseModal(false)}
          isLoading={isClosing}
          title="Confirm Close Job"
          content={
            <div>
              <div className="font-semibold">
                Are you sure you want to close this job?
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  Once closed, the job will no longer accept applications.
                </p>
              </div>
            </div>
          }
          confirmButtonText="Close Job"
          onConfirm={() => handleCloseJob(selectedJob as Job)}
        />
      )}
    </>
  );
}
