import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function updateApplyMode(
  jobId: string,
  params: { applyMode: string[]; orgId: string }
): Promise<Job | any> {
  try {
    const response = await axiosInstance.patch<Job>(
      API_ENDPONTS.UPDATE_APPLY_MODE.replace(':jobId', jobId),
      params
    );
    return response.data;
  } catch (error: any) {
    return {
      isSuccess: false,
      message: error?.response?.data?.message,
    };
  }
}

export const useUpdateApplyMode = (jobId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: { applyMode: string[]; orgId: string }) =>
      updateApplyMode(jobId, params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.UPDATE_JOB],
      });
    },
  });
};
