'use client';

import { LongTextCell } from '@/shared/long-text-cell';
import { orgAtom } from '@/store/organization-atom';
import ImageUploader from '@/views/image-uploader';
import { useAtom } from 'jotai';
import { CameraIcon, Copy, CopyCheck, Globe, Mail, MapPin } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { ActionIcon, Button } from 'rizzui';

interface OrgAdminProfileLayoutProps {
  children: React.ReactNode;
  saveChangeProps?: React.ComponentProps<typeof Button>;
  onChangeFile: (file: File | null, type: 'logo' | 'banner') => void;
  isEdit: boolean;
}

export default function OrgAdminProfileLayout(
  props: OrgAdminProfileLayoutProps
) {
  const { children, saveChangeProps, onChangeFile, isEdit } = props;

  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [copied, setCopied] = useState(false);

  const [org] = useAtom(orgAtom);

  const handleCopyProfileLink = () => {
    navigator.clipboard.writeText(
      `${process.env.NEXT_PUBLIC_BASE_URL}/org/${org?._id || ''}`
    );
    setCopied(true);
    if (!copied) {
      toast.success('Profile link copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const logoPreview = useMemo(
    () => (logoFile ? URL.createObjectURL(logoFile) : ''),
    [logoFile]
  );
  const bannerPreview = useMemo(
    () => (bannerFile ? URL.createObjectURL(bannerFile) : ''),
    [bannerFile]
  );

  useEffect(() => {
    return () => {
      if (logoPreview) URL.revokeObjectURL(logoPreview);
      if (bannerPreview) URL.revokeObjectURL(bannerPreview);
    };
  }, [logoPreview, bannerPreview]);

  return (
    <>
      <div className="mx-auto overflow-hidden">
        <div className="relative">
          {bannerPreview || org?.banner ? (
            <img
              src={bannerPreview || org?.banner}
              alt="Banner"
              className="h-[300px] w-full overflow-hidden rounded-2xl bg-white object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling;
                if (fallback) {
                  (fallback as HTMLElement).style.display = 'block';
                }
              }}
            />
          ) : (
            <div className={`h-[300px] w-full rounded-2xl bg-primary`} />
          )}

          <div className="absolute -bottom-20 left-1/2 w-fit -translate-x-1/2 transform items-center gap-4 rounded-full bg-white p-0.5 xl:left-5 xl:-translate-x-0 xl:transform-none">
            <div className="relative h-[180px] w-[180px]">
              {logoPreview || org?.logo ? (
                <img
                  src={logoPreview || org?.logo}
                  alt="Logo"
                  className="h-full w-full overflow-hidden rounded-full border-4 border-[#3E5C76] bg-white shadow-md"
                />
              ) : (
                <div className="h-full w-full rounded-full border-4 border-[#3E5C76] bg-primary" />
              )}

              <div className="absolute bottom-2 left-1/2 -translate-x-1/2">
                <ImageUploader
                  onChange={(file: File | null) => {
                    onChangeFile(file, 'logo');
                    setLogoFile(file);
                  }}
                >
                  {({ getRootProps }) => (
                    <ActionIcon
                      {...getRootProps()}
                      size="sm"
                      rounded="full"
                      className="bg-gray-200 hover:bg-gray-100"
                    >
                      <CameraIcon className="h-5 w-5 text-gray-600" />
                    </ActionIcon>
                  )}
                </ImageUploader>
              </div>
            </div>
          </div>

          <div className="absolute bottom-4 right-4">
            <ImageUploader
              onChange={(file: File | null) => {
                onChangeFile(file, 'banner');
                setBannerFile(file);
              }}
            >
              {({ getRootProps }) => (
                <ActionIcon
                  {...getRootProps()}
                  size="sm"
                  rounded="full"
                  className="bg-gray-200 hover:bg-gray-100"
                >
                  <CameraIcon className="h-5 w-5 text-gray-600" />
                </ActionIcon>
              )}
            </ImageUploader>
          </div>
        </div>

        <div className="mt-20 p-5 xl:mt-0">
          <div className="xl:ml-52">
            <div className="flex items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold">{org?.name}</h1>
                <div className="mt-1 flex flex-wrap items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <MapPin size={16} />
                    <span>{org?.location}</span>
                  </div>
                  {/* <div className="flex items-center gap-1">
              <Globe size={16} />
              <a
                href={'/'}
                className="text-blue-600 hover:underline"
                target="_blank"
              >
                https://www.meridianglobal.edu.sg
              </a>
            </div>
            <div className="flex items-center gap-1">
              <Mail size={16} />
              <a
                href={`mailto:<EMAIL>`}
                className="text-blue-600 hover:underline"
              >
                <EMAIL>
              </a>
            </div> */}
                  {org?.website && (
                    <div className="flex items-center gap-1">
                      <Globe size={16} />
                      <a
                        href={org.website}
                        className="text-blue-600 hover:underline"
                        target="_blank"
                      >
                        {org.website}
                      </a>
                    </div>
                  )}
                  {org?.email && (
                    <div className="flex items-center gap-1">
                      <Mail size={16} />
                      <a
                        href={`mailto:${org.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {org.email}
                      </a>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <ActionIcon
                  variant="outline"
                  title={copied ? 'Copied!' : 'Copy profile link'}
                  className="bg-white rounded-xl"
                  onClick={handleCopyProfileLink}
                >
                  {copied ? (
                    <CopyCheck className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </ActionIcon>
                <Button className="bg-primary text-white" {...saveChangeProps}>
                  {isEdit ? 'Save Changes' : 'Edit Profile'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {org?.description && !isEdit && (
          <div className="mt-5 rounded-2xl bg-white p-5 border border-[#c3c3c3]">
            <p className="mb-2 font-semibold text-gray-500">
              Short Description:
            </p>
            <div>
              <LongTextCell
                textClassName="whitespace-pre-line leading-relaxed"
                text={org.description}
                lines={3}
              />
            </div>
          </div>
        )}
      </div>
      <div className="mt-10">{children}</div>
    </>
  );
}
