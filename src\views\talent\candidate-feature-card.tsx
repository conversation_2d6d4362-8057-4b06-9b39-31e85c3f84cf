'use client';

import Image from 'next/image';

const features = [
  {
    title: 'AI-Powered Search',
    description: `Get intelligent candidate suggestions based on your job title, keywords, and search filters.
The system learns and continuously improves to match your hiring needs more accurately.`,
    image: '/employer/ai-powered-search.png',
  },
  {
    title: 'Simulation-Verified Candidates',
    description: `Prioritize candidates who have completed Job Simulations and earned scores that reflect real-world skills.
Easily identify those who are truly capable with certificates and performance data.`,
    image: '/employer/simulation-verified-candidates.png',
  },
  {
    title: 'Real-Time Matching',
    description: `Instantly view how well candidates match your criteria based on skills, experience, and industry.
Make faster decisions without manual screening.`,
    image: '/employer/real-time-matching.png',
  },
];

export default function TalentFeatureCard() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      {features.map((feature, idx) => (
        <div
          key={idx}
          className="hover:scale-[1.02] rounded-xl bg-white p-8 text-center border border-[#c3c3c3]"
        >
          <Image
            src={feature.image}
            alt={feature.title}
            width={160}
            height={160}
            className="mx-auto mb-6 h-[160px] w-[160px] object-contain"
            loader={({ src }) => src}
          />
          <h3 className="mb-2 text-lg font-semibold text-gray-900">
            {feature.title}
          </h3>
          <p className="whitespace-pre-line text-sm text-gray-600">
            {feature.description}
          </p>
        </div>
      ))}
    </div>
  );
}
