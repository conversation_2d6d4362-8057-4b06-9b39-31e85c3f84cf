'use client';

import React from 'react';
import PlusIcon from '@/views/icons/plus';
import { Button } from 'rizzui/button';
import Link from 'next/link';

interface IProps {
  title: React.ReactNode | string;
  description?: React.ReactNode | string;
  action?: React.ReactNode;
  actionText?: string;
  actionHref?: string;
  actionIcon?: React.ReactNode;
}

export default function PageHeader({
  title,
  description,
  action,
  actionText,
  actionHref,
  actionIcon,
}: IProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-lg font-bold sm:text-xl">{title}</h1>
        {description && (
          <div className="text-sm text-slate-500">{description}</div>
        )}
      </div>

      {action ? (
        action
      ) : (
        <Link href={actionHref || '#'}>
          <Button className="gap-1 hover:bg-primary/80">
            {actionIcon || <PlusIcon />}
            <span>{actionText}</span>
          </Button>
        </Link>
      )}
    </div>
  );
}
