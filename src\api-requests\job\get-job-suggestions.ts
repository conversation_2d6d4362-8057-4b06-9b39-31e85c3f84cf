import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { Job, JobQueryKeys, JobListParams } from './types';
import { cleanQueryParams } from '@/utils/url';
import { ApiListResponse } from '../types';

export async function listJobSuggestions(
  params: JobListParams
): Promise<Job[]> {
  const reps = await axiosInstance.get<Job[]>(
    API_ENDPONTS.GET_JOB_SUGGESTIONS,
    {
      params: cleanQueryParams(params),
    }
  );
  return reps.data;
}

export function useListJobSuggestions(params: JobListParams) {
  return useQuery<Job[]>({
    queryKey: [JobQueryKeys.GET_JOB_SUGGESTIONS, params],
    queryFn: () => listJobSuggestions(params),
    enabled: !!params,
  });
}
