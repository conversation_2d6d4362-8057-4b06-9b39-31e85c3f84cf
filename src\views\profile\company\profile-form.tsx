'use client';

import cn from '@/utils/class-names';
import FieldLabel from '@/views/job-creation/field-label';
import { Controller, useForm, useFormContext } from 'react-hook-form';
import { Input, Select, Text, Textarea } from 'rizzui';
import countriesData from '@/data/countries.json';
import { useMemo } from 'react';
import { FormValues } from './index';

interface RowProps {
  label: string;
  placeholder?: string;
  prefix?: React.ReactNode;
  icon?: React.ReactNode;
  textarea?: boolean;
  shortDescription?: React.ReactNode;
  component?: React.ReactNode;
  textareaProps?: React.ComponentProps<typeof Textarea>;
  inputProps?: React.ComponentProps<typeof Input>;
  error?: string;
}

function FormRow({
  label,
  placeholder,
  prefix,
  icon,
  textarea,
  shortDescription,
  textareaProps,
  inputProps,
  component,
  error,
}: RowProps) {
  return (
    <div className="flex flex-col items-start gap-y-2 border-b border-[#c3c3c3] py-5 sm:flex-row sm:items-center sm:gap-y-0">
      <div className="w-full pr-4 sm:w-1/4">
        <FieldLabel title={label || ''} />
        {shortDescription}
      </div>

      <div className="relative w-full sm:w-3/4">
        {component || (
          <>
            <Input
              {...inputProps}
              placeholder={placeholder}
              variant="flat"
              {...(prefix
                ? {
                    prefix: prefix,
                    prefixClassName: `h-full px-4 flex items-center absolute left-0 bg-[#DFDFDF] rounded-l-xl ${
                      prefix ? '' : ''
                    }`,
                  }
                : {})}
              className="w-full rounded-lg"
              inputClassName={cn('rounded-lg', prefix ? 'pl-20' : '')}
            />
            {error && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {error}
              </Text>
            )}
          </>
        )}

        {icon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
}

export default function CompanyForm() {
  const {
    control,
    formState: { errors },
  } = useFormContext<FormValues>();

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  return (
    <div>
      <Controller
        name="name"
        control={control}
        rules={{
          required: 'Company name is required',
        }}
        render={({ field }) => (
          <FormRow
            label="Company name"
            placeholder="Enter the company name"
            inputProps={{ ...field }}
            error={errors.name?.message}
          />
        )}
      />
      <Controller
        name="address"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            label="Address"
            placeholder="Enter address"
            inputProps={{ ...field }}
          />
        )}
      />
      <Controller
        name="city"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            label="City"
            placeholder="Enter city"
            inputProps={{ ...field }}
          />
        )}
      />
      <Controller
        name="region"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            label="Region"
            placeholder="Enter region"
            inputProps={{ ...field }}
          />
        )}
      />
      <Controller
        name="country"
        control={control}
        rules={{
          required: 'Country is required',
        }}
        render={({ field }) => (
          <FormRow
            label="Country"
            component={
              <Select
                {...field}
                clearable
                onClear={() => field.onChange(null)}
                options={countryOptions}
                placeholder="Select your country"
                className="w-full [&_.rizzui-select-button]:!rounded-lg"
                searchable={true}
                helperText={
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.country?.message}
                  </Text>
                }
              />
            }
          />
        )}
      />
      <Controller
        name="website"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            label="Website"
            placeholder="Enter company website"
            prefix="https://"
            inputProps={{ ...field }}
          />
        )}
      />
      <Controller
        name="email"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            label="Email"
            placeholder="Enter the company’s email"
            inputProps={{ ...field }}
          />
        )}
      />
      <Controller
        name="description"
        control={control}
        rules={{
          required: false,
        }}
        render={({ field }) => (
          <FormRow
            shortDescription={
              <div className="text-[12px] text-gray-500">
                Write a short overview of your company for your public profile.
              </div>
            }
            label="Short description"
            placeholder="Write product description here..."
            component={
              <Textarea
                {...field}
                placeholder="Write product description here..."
                variant="flat"
                className="w-full rounded-lg"
                textareaClassName="rounded-lg"
              />
            }
          />
        )}
      />
    </div>
  );
}
