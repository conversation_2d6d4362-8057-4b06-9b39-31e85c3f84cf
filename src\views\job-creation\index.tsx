'use client';

import { But<PERSON> } from 'rizzui';
import Stepper from './stepper';
import StepOne from './step-one';
import { useAtom } from 'jotai';
import { jobCreationStepAtom } from '@/store/job-creation-atom';
import { useCallback, useEffect, useMemo, useState } from 'react';
import ArrowLeftIcon from '../icons/arrow-left';
import ArrowRightIcon from '../icons/arrow-right';
import StepThree from './step-three';
import StepFour from './step-four';
import { FormProvider, useForm } from 'react-hook-form';
import { useCreateJob } from '@/api-requests/job/create-job';
import toast from 'react-hot-toast';
import { Job, QuestionType, Salary } from '@/api-requests/job';
import { API_DOMAINS } from '@/config/endpoint';
import { parseApplyMode } from './helper';
import { useUpdateJob } from '@/api-requests/job/update-job';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import StepTwo from './step-two';
import countriesData from '@/data/countries.json';
import timezonesData from '@/data/timezones.json';
import currenciesData from '@/data/currencies.json';
import { isValid } from 'date-fns';
import ConfirmPublishModal from './confirm-publish-modal';
import PageHeader from '@/shared/page-header';
import { SelectOption } from '@/api-requests/types';

export type FormValues = {
  title: string;
  categories?: string[];
  levels?: string[];
  applyMode?: string[];
  address?: string;
  city?: string;
  region?: string;
  country: {
    label: string;
    value: string;
  } | null;
  workTypes?: string[];
  workPlaces?: string[];
  salary: {
    min?: number;
    max?: number;
    currency?: SelectOption | null;
    period?: SelectOption | null;
    type: string;
  };
  experience: {
    min?: number;
    max?: number;
  };
  description?: string;
  skills: string[];
  expiresAt?: Date | null;
  applicationLimit?: number | null;
  timezone?: {
    label: string;
    value: string;
  } | null;
  quickQuestions: {
    id: string;
    text: string;
    type: {
      label: string;
      value: string;
    } | null;
    required: boolean;
    options?: { id: string; value: string }[];
    minLength?: number;
    maxLength?: number;
  }[];
};

const questionOptions = [
  { label: 'Yes/No', value: QuestionType.YES_NO },
  { label: 'Single Choice', value: QuestionType.SINGLE_CHOICE },
  { label: 'Multiple Choice', value: QuestionType.MULTI_CHOICE },
  { label: 'Numer', value: QuestionType.NUMBER },
  { label: 'Short Text', value: QuestionType.SHORT_TEXT },
  { label: 'Long Text', value: QuestionType.LONG_TEXT },
];

export const periodOptions = [
  { label: 'Hourly', value: 'hourly' },
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' },
  { label: 'Yearly', value: 'yearly' },
];

interface IProps {
  job?: Job | null;
  type?: 'create' | 'update';
}

export default function JobCreation({ job: jobData, type }: IProps) {
  const router = useRouter();
  const [job, setJob] = useState<Job | null>(jobData || null);

  const [openPublishModal, setOpenPublishModal] = useState(false);

  const { data, mutateAsync, isPending } = useCreateJob();
  const { mutateAsync: updateMutateAsync, isPending: isUpdating } =
    useUpdateJob(job?.jobId || '');
  const [step, setStep] = useAtom(jobCreationStepAtom);

  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const applyMode = useMemo(() => {
    return parseApplyMode(job?.applyMode as string);
  }, [job?.applyMode]);

  const country = useMemo(() => {
    if (job) {
      const found = countriesData.find(
        (item) =>
          item.name === job.country ||
          item.code === job.countryCode ||
          item.code === userTimeZone
      );
      if (found) {
        return { label: found.name, value: found.code };
      }
    }
    return null;
  }, [job?.country]);

  const currency = useMemo(() => {
    if (job) {
      const found = currenciesData.find(
        (item) => item.code === (job?.salary as Salary)?.currency
      );
      if (found) {
        return { label: found.code, value: found.code };
      }
    }
    return { label: 'USD', value: 'USD' };
  }, [job?.country]);

  const timezone = useMemo(() => {
    if (job && job.timezone) {
      const found = timezonesData.find((item) => item.id === job.timezone);
      if (found) {
        return { label: found.id, value: found.id };
      }
    }
    return null;
  }, [job?.timezone]);

  const period = useMemo(() => {
    if (job && (job?.salary as Salary)?.period) {
      const found = periodOptions.find(
        (item) => item.value === (job?.salary as Salary)?.period
      );
      if (found) {
        return found;
      }
    }
    return periodOptions[3];
  }, [job?.timezone]);

  // Memoize defaultValues để tránh tạo lại object mỗi render
  const defaultValues = useMemo(
    () => ({
      title: job?.title || '',
      categories: job?.categories || [],
      levels: job?.levels || [],
      address: job?.address || '',
      city: job?.city || '',
      region: job?.region || '',
      country: country,
      workTypes: Array.isArray(job?.workTypes) ? job?.workTypes : [],
      workPlaces: Array.isArray(job?.workPlaces) ? job?.workPlaces : [],
      salary: {
        min: (job?.salary as Salary)?.min || 1000,
        max: (job?.salary as Salary)?.max || 100000,
        currency: currency,
        period: period,
        type: (job?.salary as Salary)?.type || 'range',
      },
      description: job?.description || '',
      skills: job?.skills || [],
      expiresAt: isValid(new Date(job?.expiresAt as Date))
        ? new Date(job?.expiresAt as Date)
        : null,
      applyMode: applyMode.length > 0 ? applyMode : ['simulation', 'cv'],
      applicationLimit: job?.applicationLimit || null,
      timezone: timezone,
      quickQuestions:
        job?.quickQuestions?.map((item) => {
          const type = questionOptions.find((q) => q.value === item.type);
          return { ...item, type };
        }) || [],
    }),
    [job, country, timezone, applyMode]
  );

  const methods = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: defaultValues,
  });

  useEffect(() => {
    if (job) {
      methods.reset(defaultValues);
    }
  }, [job, defaultValues, methods]);

  const renderStepContent = useCallback(() => {
    switch (step) {
      case 1:
        return <StepOne />;
      case 2:
        return <StepTwo />;
      case 3:
        return <StepThree />;
      case 4:
        return <StepFour />;
      default:
        return '';
    }
  }, [step]);

  const onSubmitDraft = async (payload: FormValues) => {
    const jobData = {
      workTypes: payload.workTypes,
      workPlaces: payload.workPlaces,
      salary: {
        ...payload.salary,
        currency: payload.salary.currency?.value,
        period: payload.salary.period?.value,
      },
      title: payload.title,
      categories: payload.categories,
      levels: payload.levels,
      description: payload.description,
      skills: payload.skills,
      jobUrl: API_DOMAINS.BASE_URL + '/find-jobs',
      expiresAt: payload.expiresAt,
      isPublished: false,
      applyMode: payload.applyMode,
      applicationLimit: payload.applicationLimit,
      country: payload.country?.label || '',
      countryCode: payload.country?.value || '',
      region: payload.region || '',
      city: payload.city || '',
      address: payload.address || '',
      quickQuestions: payload.quickQuestions.map((q) => ({
        ...q,
        type: q.type?.value as QuestionType,
      })),
    } as Job;

    let resp = null;
    if (type === 'update') {
      resp = await updateMutateAsync(jobData);
    } else {
      resp = await mutateAsync(jobData);
    }

    if (resp) {
      toast.success(
        `Job ${type === 'update' ? 'updated' : 'created'} successfully`
      );
      setStep(1);
      if (type === 'update' && job) {
        setJob(resp);
      }
    } else {
      toast.error(`Failed to ${type === 'update' ? 'update' : 'create'} job`);
    }
  };

  const onSubmit = async (payload: FormValues) => {
    const jobData = {
      workTypes: payload.workTypes,
      workPlaces: payload.workPlaces,
      salary: {
        ...payload.salary,
        currency: payload.salary.currency?.value,
        period: payload.salary.period?.value,
      },
      title: payload.title,
      categories: payload.categories,
      levels: payload.levels,
      description: payload.description,
      skills: payload.skills,
      jobUrl: API_DOMAINS.BASE_URL + '/find-jobs',
      expiresAt: payload.expiresAt,
      isPublished: true,
      applyMode: payload.applyMode,
      applicationLimit: payload.applicationLimit,
      country: payload.country?.label || '',
      countryCode: payload.country?.value || '',
      region: payload.region || '',
      city: payload.city || '',
      address: payload.address || '',
      quickQuestions: payload.quickQuestions.map((q) => ({
        ...q,
        type: q.type?.value as QuestionType,
      })),
    } as Job;

    let resp = null;
    if (type === 'update') {
      resp = await updateMutateAsync(jobData);
    } else {
      resp = await mutateAsync(jobData);
    }
    if (resp && resp.isSuccess !== false) {
      toast.success(
        `Job ${type === 'update' ? 'updated' : 'created'} successfully`
      );
      setStep(1);
      setOpenPublishModal(false);
      if (type === 'update' && job) {
        setJob(resp);
      }
    } else {
      toast.error(
        resp?.message ||
          `Failed to ${type === 'update' ? 'update' : 'create'} job`
      );
    }
  };

  const onNext = () => {
    setStep((prev) => prev + 1);
  };

  const hanleBack = () => {
    setStep((prev) => prev - 1);
  };

  return (
    <>
      <div>
        <PageHeader
          title={type === 'update' ? 'Update Job' : 'Create a New Job'}
          actionText="Cancel"
          actionHref="/org/admin/jobs"
          actionIcon={<ArrowLeftIcon className="h-5 w-5" />}
        />

        <hr className="my-4" />

        <Stepper />

        <div className="mx-auto max-w-[792px] space-y-6 py-10">
          <FormProvider {...methods}>{renderStepContent()}</FormProvider>

          <hr className="border-t border-[#c3c3c3]" />

          <div className="flex flex-wrap items-center justify-between gap-4">
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={hanleBack}
              disabled={step === 1}
            >
              <ArrowLeftIcon className="h-5 w-5" />
              Back
            </Button>

            <Button
              className="text-[#161616] underline underline-offset-4 hover:opacity-80"
              variant="flat"
              onClick={methods.handleSubmit(onSubmitDraft)}
              disabled={
                !methods.formState.isValid ||
                isPending ||
                isUpdating ||
                methods.formState.isSubmitting ||
                step !== 4
              }
              isLoading={
                isPending || isUpdating || methods.formState.isSubmitting
              }
            >
              Save as Draft
            </Button>

            {step === 4 ? (
              <div className="flex items-center gap-4">
                {/* <Button
                className="flex items-center gap-2 bg-primary text-white"
                onClick={methods.handleSubmit(onSubmitDraft)}
                disabled={
                  !methods.formState.isValid ||
                  isPending ||
                  isUpdating ||
                  methods.formState.isSubmitting
                }
                isLoading={
                  isPending || isUpdating || methods.formState.isSubmitting
                }
              >
                Create
                <ArrowRightIcon className="h-5 w-5 text-white" />
              </Button> */}
                <Button
                  className="flex items-center gap-1 bg-primary text-white"
                  onClick={methods.handleSubmit(() =>
                    setOpenPublishModal(true)
                  )}
                  disabled={
                    !methods.formState.isValid ||
                    isPending ||
                    isUpdating ||
                    methods.formState.isSubmitting
                  }
                  isLoading={
                    isPending || isUpdating || methods.formState.isSubmitting
                  }
                >
                  Create & Publish
                  <ArrowRightIcon className="h-5 w-5 text-white" />
                </Button>
              </div>
            ) : (
              <Button
                className="flex items-center gap-1 bg-primary text-white"
                onClick={methods.handleSubmit(onNext)}
              >
                Next
                <ArrowRightIcon className="h-5 w-5 text-white" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {openPublishModal && (
        <ConfirmPublishModal
          open={openPublishModal}
          onClose={() => setOpenPublishModal(false)}
          isLoading={isPending || isUpdating || methods.formState.isSubmitting}
          onPublish={methods.handleSubmit(onSubmit)}
        />
      )}
    </>
  );
}
