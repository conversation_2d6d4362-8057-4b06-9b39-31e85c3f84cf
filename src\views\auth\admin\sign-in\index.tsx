'use client';

import { useCallback, useState } from 'react';
import { ActionType } from '../../sign-in-up/sign-in-modal';
import { FormProvider, useForm } from 'react-hook-form';
import { OrganizationType } from '@/api-requests/organization';
import { Role } from '@/api-requests/user';
import Image from 'next/image';
import EmailForm from './email-form';
import PasswordForm from './password-form';
import VerifyCodeForm from './verify-code-form';
import { useRouter } from 'next/navigation';
import ForgotPasswordForm from './forgot-password-form';

type FormValues = {
  email: string;
  password?: string;
  code?: string[];
  newPassword?: string;
  confirmPassword?: string;
};

export default function AdminSignIn() {
  const router = useRouter();

  const [actionType, setActionType] = useState<string>(ActionType.SIGN_IN);
  const [verifyType, setVerifyType] = useState<string>(ActionType.SIGN_UP);

  const handleLoginSuccess = (respUser?: any) => {
    router.push('/admin/candidates');
  };

  const methods = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      code: Array(6).fill(''),
      newPassword: '',
      confirmPassword: '',
    },
  });

  const renderContent = useCallback(() => {
    switch (actionType) {
      case ActionType.CONTINUE_WITH_PASSWORD:
        return (
          <PasswordForm
            onSetAction={setActionType}
            onClose={() => {}}
            onLoginSuccess={handleLoginSuccess}
          />
        );
      case ActionType.VERIFY_CODE:
        return (
          <VerifyCodeForm
            onSetAction={setActionType}
            onClose={() => {}}
            verifyType={verifyType}
            role={Role.ADMIN}
            onLoginSuccess={handleLoginSuccess}
          />
        );
      case ActionType.RESET_PASSWORD:
        return (
          <ForgotPasswordForm
            onSetAction={setActionType}
            onVerifyType={setVerifyType}
          />
        );
      // case ActionType.SIGN_UP:
      //   return (
      //     <SignUpForm
      //       onSetAction={setActionType}
      //       onVerifyType={setVerifyType}
      //       role={role}
      //     />
      //   );
      //   case ActionType.COMPANY_SIGN_UP:
      //     return (
      //       <CompanyForm
      //         onSetAction={setActionType}
      //         onVerifyType={setVerifyType}
      //         role={role}
      //       />
      //     );
      default:
        return (
          <EmailForm
            onSetAction={setActionType}
            onVerifyType={setVerifyType}
            onLoginSuccess={() => {}}
          />
        );
    }
  }, [actionType, setActionType, setVerifyType, verifyType]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="w-full max-w-[500px]">
        <div className="w-full space-y-6">
          <div className="flex w-full justify-center">
            <Image
              src={'/ic-io-logo-light.png'}
              alt="Industry Connect Logo"
              width={0}
              height={48}
              className="h-12 w-auto"
              loader={({ src }) => src}
            />
          </div>

          <FormProvider {...methods}>{renderContent()}</FormProvider>
        </div>
      </div>
    </div>
  );
}
