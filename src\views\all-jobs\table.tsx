'use client';

import { Job, JobStatus } from '@/api-requests/job';
import { useCloseJob } from '@/api-requests/job/close-job';
import { usePublishJob } from '@/api-requests/job/publish-job';
import { useUnpublishJob } from '@/api-requests/job/unpublish-job';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import ConfirmModal from '@/shared/confirm-modal';
import { orgAtom } from '@/store/organization-atom';
import cn from '@/utils/class-names';
import { safeFormatDate } from '@/utils/date';
import DeleteIcon from '@/views/icons/delete';
import EditIcon from '@/views/icons/edit';
import EditSquareIcon from '@/views/icons/edit-square';
import Pagination from '@/views/pagination';
import { useAtom } from 'jotai';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import toast from 'react-hot-toast';
import {
  Badge,
  Dropdown,
  Loader,
  Table
} from 'rizzui';
import CloseIcon from '../icons/close';
import MoreHorizontalIcon from '../icons/more-horizontal';
import PublishedIcon from '../icons/published';
import UnpublishedIcon from '../icons/unpublished';
import ConfirmPublishModal from '../job-creation/confirm-publish-modal';
import {
  getJobTypeString,
  simulationLevel
} from '../job/job-list';
import DeleteJobModal from './delete-job-modal';

export const jobStatusClasses: Record<string, string> = {
  [JobStatus.UNPUBLISHED]:
    'border border-[#FF7830] bg-[#FFCCAC] text-[#FF7830]',
  [JobStatus.PUBLISHED]: 'border border-[#00BC54] bg-[#BAFFC7] text-[#00BC54]',
  [JobStatus.DRAFT]: 'border border-[#0D1321] text-[#0D1321]',
  [JobStatus.CLOSED]: 'border border-[#FF5555] bg-[#FFE8E8] text-[#FF5555]',
};

export const getJobStatusClasses = (status: JobStatus) => {
  return jobStatusClasses[status] || 'border border-[#0D1321] text-[#0D1321]';
};

interface IProps {
  jobData: ApiListResponse<Job>;
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  refetch: () => void;
}

export default function AllJobsTable({
  jobData,
  isLoading,
  page,
  setPage,
  refetch,
}: IProps) {
  const router = useRouter();

  const [org] = useAtom(orgAtom);

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openUnpublishModal, setOpenUnpublishModal] = useState(false);
  const [openCloseModal, setOpenCloseModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  const { mutateAsync: publishMutateAsync, isPending: isPublishing } =
    usePublishJob();
  const { mutateAsync: unpublishMutateAsync, isPending: isUnpublishing } =
    useUnpublishJob();
  const { mutateAsync: closeMutateAsync, isPending: isClosing } = useCloseJob();

  const handleView = (id: string) => {
    router.push(`/org/admin/jobs/${id}`);
  };

  const handleEdit = (id: string) => {
    router.push(`/org/admin/jobs/edit/${id}`);
  };

  const handlePublishJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await publishMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job published successfully');
      setOpenPublishModal(false);
    } else {
      toast.error('Failed to publish job');
    }
  };

  const handleUnpublishJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await unpublishMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job unpublished successfully');
      setOpenUnpublishModal(false);
    } else {
      toast.error('Failed to unpublish job');
    }
  };

  const handleCloseJob = async (job: Job) => {
    if (!org?._id) return;
    const resp = await closeMutateAsync({
      jobId: job.jobId,
      orgId: org?._id as string,
    });
    if (resp) {
      refetch();
      toast.success('Job closed successfully');
      setOpenCloseModal(false);
    } else {
      toast.error('Failed to close job');
    }
  };

  return (
    <>
      <div className="overflow-x-auto rounded-xl bg-white pb-5">
        <Table variant="modern">
          <Table.Header className="rounded-t-xl border-b border-[#c3c3c3] !bg-white">
            <Table.Row className="rounded-t-xl">
              {/* <Table.Head className="w-16"></Table.Head> */}
              <Table.Head>Job title</Table.Head>
              <Table.Head>Candidates</Table.Head>
              <Table.Head>Simulation</Table.Head>
              <Table.Head>Status</Table.Head>
              <Table.Head>Created At</Table.Head>
              <Table.Head className="!text-right">Actions</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {jobData?.data?.map((job, index) => (
              <Table.Row key={index} className="border-b border-[#c3c3c3]">
                {/* <Table.Cell>
                  <Avatar
                    src={job.companyLogoUrl}
                    name={job.title}
                    customSize={40}
                  />
                </Table.Cell> */}

                <Table.Cell>
                  <div
                    className="cursor-pointer whitespace-nowrap text-base font-bold text-slate-600 transition-colors hover:text-slate-900 hover:underline"
                    onClick={() => handleView(job.jobId)}
                  >
                    {job.title}
                  </div>
                  <span className="text-sm text-gray-500">
                    {job?.city}
                    {job?.city ? ', ' : ''}
                    {job?.country} • {getJobTypeString(job)}
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <div className="font-bold">{job.applicants}</div>
                  <div className="text-sm">
                    <div className="text-xs text-gray-500">
                      Last Applied:{' '}
                      {safeFormatDate(job.lastAppliedAt, {
                        fallback: '-',
                      })}
                    </div>
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm">
                    {simulationLevel[job.simulation?.level as number] || '-'}
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={cn(
                      jobStatusClasses[String(job.status)],
                      'px-2 py-0.5 text-[12px] font-medium'
                    )}
                  >
                    {job.status}
                  </Badge>
                </Table.Cell>

                <Table.Cell>
                  <div className="text-sm">
                    {safeFormatDate(job._createdAt, { fallback: '-' })}
                  </div>
                  <div className="text-xs text-gray-500">
                    Updated: {safeFormatDate(job.updatedAt, { fallback: '-' })}
                  </div>
                </Table.Cell>

                <Table.Cell className="text-right">
                  <Dropdown placement="bottom-end">
                    <Dropdown.Trigger>
                      <MoreHorizontalIcon />
                    </Dropdown.Trigger>
                    <Dropdown.Menu className="w-fit divide-y">
                      {[
                        {
                          icon: <EditSquareIcon className="h-5 w-5" />,
                          label: 'View Detail',
                          onClick: (job: Job) => handleView(job.jobId),
                        },
                        {
                          icon: <EditIcon className="h-5 w-5" />,
                          label: 'Edit',
                          onClick: (job: Job) => handleEdit(job.jobId),
                        },
                        {
                          icon: <DeleteIcon className="h-5 w-5" />,
                          label: 'Delete',
                          onClick: (job: Job) => {
                            setSelectedJob(job);
                            setOpenDeleteModal(true);
                          },
                        },
                        ...((job.status === JobStatus.PUBLISHED && [
                          {
                            icon: <UnpublishedIcon className="h-5 w-5" />,
                            label: 'Unpublish Job',
                            onClick: (job: Job) => {
                              setSelectedJob(job);
                              setOpenUnpublishModal(true);
                            },
                          },
                        ]) ||
                          []),
                        ...(([JobStatus.UNPUBLISHED, JobStatus.DRAFT].includes(
                          job.status
                        ) && [
                          {
                            icon: <PublishedIcon className="h-5 w-5" />,
                            label: 'Publish Job',
                            onClick: (job: Job) => {
                              setSelectedJob(job);
                              setOpenPublishModal(true);
                            },
                          },
                        ]) ||
                          []),
                        ...(([JobStatus.PUBLISHED].includes(job.status) && [
                          {
                            icon: <CloseIcon className="h-5 w-5" />,
                            label: 'Close Job',
                            onClick: (job: Job) => {
                              setSelectedJob(job);
                              setOpenCloseModal(true);
                            },
                          },
                        ]) ||
                          []),
                      ].map((action, idx) => (
                        <Dropdown.Item
                          key={idx}
                          className="hover:bg-primary hover:text-white"
                          onClick={() => action.onClick && action.onClick(job)}
                        >
                          <div className="flex items-center">
                            {action.icon}
                            <span className="ml-2">{action.label}</span>
                          </div>
                        </Dropdown.Item>
                      ))}
                    </Dropdown.Menu>
                  </Dropdown>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-40 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              jobData?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No jobs found</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-[#c3c3c3] pt-4" />

        <Pagination
          total={jobData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>

      {openDeleteModal && (
        <DeleteJobModal
          open={openDeleteModal}
          onClose={() => {
            setSelectedJob(null);
            setOpenDeleteModal(false);
            refetch();
          }}
          job={selectedJob as Job}
        />
      )}

      {openPublishModal && (
        <ConfirmPublishModal
          open={openPublishModal}
          onClose={() => setOpenPublishModal(false)}
          isLoading={isPublishing}
          onPublish={() => handlePublishJob(selectedJob as Job)}
        />
      )}

      {openUnpublishModal && (
        <ConfirmModal
          open={openUnpublishModal}
          onClose={() => setOpenUnpublishModal(false)}
          isLoading={isUnpublishing}
          title="Confirm Unpublish Job"
          content={
            <div>
              <div className="font-semibold">
                Are you sure you want to unpublish this job?
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  Once unpublished, the job will no longer be visible to users.
                </p>
              </div>
            </div>
          }
          confirmButtonText="Unpublish"
          onConfirm={() => handleUnpublishJob(selectedJob as Job)}
        />
      )}

      {openCloseModal && (
        <ConfirmModal
          open={openCloseModal}
          onClose={() => setOpenCloseModal(false)}
          isLoading={isClosing}
          title="Confirm Close Job"
          content={
            <div>
              <div className="font-semibold">
                Are you sure you want to close this job?
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  Once closed, the job will no longer accept applications.
                </p>
              </div>
            </div>
          }
          confirmButtonText="Close Job"
          onConfirm={() => handleCloseJob(selectedJob as Job)}
        />
      )}
    </>
  );
}
