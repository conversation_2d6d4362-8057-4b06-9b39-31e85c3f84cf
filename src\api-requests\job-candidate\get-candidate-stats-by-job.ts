import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import { JobCandidate, JobCandidateQueryKeys } from './types';

export async function getCandidateStatsByJob(jobId: string) {
  const reps = await axiosInstance.get(
    API_ENDPONTS.GET_CANDIDATE_STATS_BY_JOB.replace(':jobId', jobId || '')
  );

  return reps.data;
}

export function useGetCandidateStatsByJob(jobId: string) {
  return useQuery({
    queryKey: [JobCandidateQueryKeys.GET_CANDIDATE_STATS_BY_JOB, { jobId }],
    queryFn: () => getCandidateStatsByJob(jobId),
    enabled: !!jobId,
  });
}
