'use client';

import { UserCVEducation } from '@/store/user-atom';
import CloseIcon from '@/views/icons/close';
import Field<PERSON>abel from '@/views/job-creation/field-label';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  ActionIcon,
  Button,
  Input,
  Modal,
  Select,
  Textarea,
  Title,
} from 'rizzui';

interface IProps {
  open: boolean;
  isUpdating: boolean;
  initialData?: UserCVEducation & { index: number };
  onClose: () => void;
  onSave?: (data: UserCVEducation) => boolean | Promise<boolean>;
}

interface FormData {
  school: string;
  field: string;
  degree?: string;
  grade?: string;
  startMonth?: { value: number; label: string } | null;
  startYear?: { value: number; label: string } | null;
  endMonth?: { value: number; label: string } | null;
  endYear?: { value: number; label: string } | null;
  description?: string;
}

// TODO: rewrite in common files
const monthOptions = [
  { label: 'January', value: 1 },
  { label: 'February', value: 2 },
  { label: 'March', value: 3 },
  { label: 'April', value: 4 },
  { label: 'May', value: 5 },
  { label: 'June', value: 6 },
  { label: 'July', value: 7 },
  { label: 'August', value: 8 },
  { label: 'September', value: 9 },
  { label: 'October', value: 10 },
  { label: 'November', value: 11 },
  { label: 'December', value: 12 },
];

// TODO: rewrite in common files
const currentYear = new Date().getFullYear();
const yearOptions = Array.from({ length: 100 }, (_, i) => {
  const year = currentYear - i;
  return { label: year.toString(), value: year };
});

export default function EducationModal({
  open,
  isUpdating,
  initialData,
  onClose,
  onSave,
}: IProps) {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setError,
    setValue,
    clearErrors,
  } = useForm<FormData>({
    defaultValues: {
      school: '',
      field: '',
      degree: '',
      grade: '',
      startMonth: null,
      startYear: null,
      endMonth: null,
      endYear: null,
      description: '',
    },
  });

  const startMonth = watch('startMonth');
  const startYear = watch('startYear');
  const endMonth = watch('endMonth');
  const endYear = watch('endYear');

  useEffect(() => {
    if (open) {
      if (initialData) {
        reset({
          school: initialData.school || '',
          field: initialData.field || '',
          degree: initialData.degree || '',
          grade: initialData.grade || '',
          startMonth:
            initialData.startMonth === undefined
              ? null
              : monthOptions.find((m) => m.value === initialData.startMonth) ||
                null,
          startYear:
            initialData.startYear === undefined
              ? null
              : {
                  value: initialData.startYear,
                  label: initialData.startYear.toString(),
                },
          endMonth:
            initialData.endMonth === undefined
              ? null
              : monthOptions.find((m) => m.value === initialData.endMonth) ||
                null,
          endYear:
            initialData.endYear === undefined
              ? null
              : {
                  value: initialData.endYear,
                  label: initialData.endYear.toString(),
                },
          description: initialData.description || '',
        });
      } else {
        reset({
          school: '',
          field: '',
          degree: '',
          grade: '',
          startMonth: null,
          startYear: null,
          endMonth: null,
          endYear: null,
          description: '',
        });
      }
    }
  }, [open, initialData, reset]);

  // Validate startMonth and startYear dependency
  useEffect(() => {
    if (startMonth && !startYear) {
      setError('startYear', {
        type: 'required',
        message: 'Start year is required when start month is selected',
      });
    } else {
      clearErrors('startYear');
    }
  }, [startMonth, startYear, setError, clearErrors]);

  // Validate endMonth and endYear dependency
  useEffect(() => {
    if (endMonth && !endYear) {
      setError('endYear', {
        type: 'required',
        message: 'End year is required when end month is selected',
      });
    } else {
      clearErrors('endYear');
    }
  }, [endMonth, endYear, setError, clearErrors]);

  const onSubmit = async (data: FormData) => {
    // Additional validation for startMonth/startYear
    if (data.startMonth && !data.startYear) {
      setError('startYear', {
        type: 'required',
        message: 'Start year is required when start month is selected',
      });
      return;
    } else if (data.startYear && !data.startMonth) {
      // setError('startMonth', {
      //   type: 'required',
      //   message: 'Start month is required when start year is selected',
      // });
      // return;
    }

    // Additional validation for endMonth/endYear
    if (data.endMonth && !data.endYear) {
      setError('endYear', {
        type: 'required',
        message: 'End year is required when end month is selected',
      });
      return;
    } else if (data.endYear && !data.endMonth) {
      // setError('endMonth', {
      //   type: 'required',
      //   message: 'End month is required when end year is selected',
      // });
      // return;
    }

    if (data.startYear && data.endYear) {
      if (data.endYear.value < data.startYear.value) {
        setError('endYear', {
          type: 'validate',
          message: 'End year must be after start year',
        });
        return;
      }
      if (
        data.startMonth &&
        data.endMonth &&
        data.endYear.value === data.startYear.value &&
        data.endMonth.value <= data.startMonth.value
      ) {
        setError('endMonth', {
          type: 'validate',
          message: 'End month must be after start month',
        });
        return;
      }
    }

    if (!onSave) return;

    try {
      const educationData: UserCVEducation = {
        id: initialData?.id || '',
        school: data.school,
        field: data.field,
        degree: data.degree || undefined,
        grade: data.grade || undefined,
        startMonth: data.startMonth?.value || undefined,
        startYear: data.startYear?.value || undefined,
        endMonth: data.endMonth?.value || undefined,
        endYear: data.endYear?.value || undefined,
        description: data.description || undefined,
      };

      const result = await onSave(educationData);
      if (result) {
        onClose();
      }
    } catch (error) {
      console.error('Error saving education:', error);
    }
  };

  const title = initialData ? 'Edit Education' : 'Add Education';

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      size="lg"
      containerClassName="md:w-[600px]"
    >
      <div className="w-full rounded-[20px] p-6">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">{title}</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* School */}
          <div>
            <FieldLabel title="School" />
            <Controller
              name="school"
              control={control}
              rules={{ required: 'School is required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="e.g. Harvard University"
                  className="w-full"
                  error={errors.school?.message}
                  errorClassName="text-red-500 text-xs !bg-transparent"
                />
              )}
            />
          </div>

          {/* Field */}
          <div>
            <FieldLabel title="Field of study" />
            <Controller
              name="field"
              control={control}
              rules={{ required: 'Field of study is required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="e.g. Computer Science"
                  className="w-full"
                  error={errors.field?.message}
                  errorClassName="text-red-500 text-xs !bg-transparent"
                />
              )}
            />
          </div>

          {/* Degree and Grade */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldLabel title="Degree" />
              <Controller
                name="degree"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="e.g. Bachelor's"
                    className="w-full"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="Grade" />
              <Controller
                name="grade"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="e.g. 3.8 GPA"
                    className="w-full"
                  />
                )}
              />
            </div>
          </div>

          {/* Start Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldLabel title="Start Month" />
              <Controller
                name="startMonth"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={monthOptions}
                    placeholder="Select month"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('startMonth', null);
                    }}
                    className="w-full"
                    error={errors.startMonth?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="Start Year" />
              <Controller
                name="startYear"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={yearOptions}
                    placeholder="Select year"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('startYear', null);
                    }}
                    className="w-full"
                    error={errors.startYear?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
          </div>

          {/* End Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldLabel title="End Month" />
              <Controller
                name="endMonth"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={monthOptions}
                    placeholder="Select month"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('endMonth', null);
                    }}
                    className="w-full"
                    error={errors.endMonth?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="End Year" />
              <Controller
                name="endYear"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={yearOptions}
                    placeholder="Select year"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('endYear', null);
                    }}
                    className="w-full"
                    error={errors.endYear?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
          </div>

          {/* Description */}
          <div className="w-full">
            <FieldLabel title="Description" />
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  variant="flat"
                  placeholder="Describe your education experience, achievements, or relevant coursework..."
                  className="w-full"
                  rows={8}
                />
              )}
            />
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="submit"
              className="bg-primary text-white"
              disabled={isUpdating}
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
