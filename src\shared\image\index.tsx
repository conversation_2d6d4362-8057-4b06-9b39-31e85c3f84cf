'use client';

import NextImage from 'next/image';
import { useState } from 'react';
interface IProps {
  src: string;
  fallbackSrc?: string;
  alt: string;
  loader?: (props: { src: string; width: number; quality?: number }) => string;
  [key: string]: any;
}

export const Image = (props: IProps) => {
  const { src, fallbackSrc, alt, width, height, loader, ...imgProps } = props;
  const [currentSrc, setCurrentSrc] = useState<string>(
    src || fallbackSrc || ''
  );
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError && !!fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setHasError(true);
    }
  };

  return !!currentSrc ? (
    <NextImage
      src={currentSrc}
      alt={alt}
      onError={handleError}
      loader={loader}
      width={width}
      height={height}
      {...imgProps}
    />
  ) : (
    <div
      className="rounded-full bg-gray-100"
      style={{ width: `${width}px`, height: `${height}px` }}
    />
  );
};
