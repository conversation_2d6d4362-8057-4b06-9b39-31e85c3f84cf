'use client';

import { setUser<PERSON>tom } from '@/store/user-atom';
import { useSet<PERSON>tom } from 'jotai';
import { useEffect, useRef } from 'react';
import { UserInfo } from '@/store/user-atom';
import { useAuthActions } from '@/hooks/use-auth-actions';

export default function AuthProvider({
  children,
  session,
}: {
  children: React.ReactNode;
  session?: { user: UserInfo } & { shouldRedirect?: boolean };
}) {
  const userRef = useRef(false);
  const { logout } = useAuthActions();
  const setUser = useSetAtom(setUserAtom);

  useEffect(() => {
    if (userRef.current) return;

    if (session?.shouldRedirect) {
      logout();
    }

    setUser(session as { user: UserInfo });

    userRef.current = true;
  }, [session, setUser]);

  return <>{children}</>;
}
