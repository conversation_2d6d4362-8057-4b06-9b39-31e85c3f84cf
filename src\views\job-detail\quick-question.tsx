'use client';

import { JobQuickQuestion } from '@/api-requests/job';
import React, { useMemo, useState } from 'react';
import { Badge } from 'rizzui';

// ========= Types =========
// export type QuestionType =
//   | 'yes_no'
//   | 'single'
//   | 'multiple'
//   | 'short_text'
//   | 'long_text';

// export type QuickQuestion = {
//   id: string;
//   type: QuestionType;
//   text: string;
//   required?: boolean;
//   options?: { id?: string; value: string }[];
//   maxLength?: number;
//   minLength?: number;
// };

// ========= Demo data (match your previous screenshot) =========
// const demoQuestions: QuickQuestion[] = [
//   {
//     id: '4f2e4c33-cde1-422f-8bce-2060aad09e51',
//     type: 'yes_no',
//     text: 'Bạn có kinh nghiệm với Next.js 14+ không?',
//     required: true,
//   },
//   {
//     id: 'ccaf6d03-c78e-40f4-996a-9523a93b8531',
//     type: 'single',
//     text: 'Chọn mức kinh nghiệm React của bạn',
//     required: true,
//     options: [
//       { value: '< 1 năm' },
//       { value: '1 - 3 năm' },
//       { value: '3 - 5 năm' },
//       { value: '5+ năm' },
//     ],
//   },
//   {
//     id: '09951a5e-d576-4f0f-8186-2080b6a44fbe',
//     type: 'multiple',
//     text: 'Các công nghệ bạn từng dùng trong production',
//     required: false,
//     options: [
//       { value: 'NestJS' },
//       { value: 'NextJS' },
//       { value: 'PostgreSQL' },
//       { value: 'MongoDB' },
//       { value: 'Docker' },
//       { value: 'Kubernetes' },
//     ],
//   },
//   {
//     id: '0b665bb7-2db5-45d0-883a-2c1f5522411e',
//     type: 'short_text',
//     text: 'Bạn quen làm việc theo mô hình nào? (Scrum/Kanban/…)',
//     required: false,
//     maxLength: 150,
//   },
//   {
//     id: '85c5eb61-469f-474c-a15a-3f61b0608121',
//     type: 'long_text',
//     text: 'Mô tả case khó nhất bạn từng giải (performance/scale)',
//     required: true,
//     minLength: 30,
//     maxLength: 600,
//   },
// ];

// ========= UI Helpers =========
const TYPE_LABEL: Record<string, string> = {
  yes_no: 'Yes / No',
  single: 'Single choice',
  multiple: 'Multiple choice',
  short_text: 'Short text',
  long_text: 'Long text',
  number: 'Number',
};

function cn(...c: Array<string | false | undefined>) {
  return c.filter(Boolean).join(' ');
}

function StatBox({ label, value }: { label: string; value: React.ReactNode }) {
  return (
    <div className="rounded-2xl border border-[#c3c3c3] p-4">
      <div className="text-gray-500">{label}</div>
      <div className="mt-1 text-lg font-semibold">{value}</div>
    </div>
  );
}

// ========= Card Shell (match your screenshot style) =========
function CardShell({
  title,
  description,
  children,
}: {
  title: string;
  description?: string;
  children: React.ReactNode;
}) {
  return (
    <>
      <div className="mb-4">
        <div className="text-lg font-semibold">{title}</div>
        <div className="text-gray-500">{description}</div>
      </div>
      {children}
    </>
  );
}

// ========= The CARD you can drop into Job Detail =========
export function JobDetailQuickQuestionsCard({
  questions,
}: {
  questions: JobQuickQuestion[];
}) {
  const stat = useMemo(() => {
    const total = questions.length;
    const required = questions.filter((q) => q.required).length;
    const byType = questions.reduce<Record<string, number>>(
      (acc, q) => ({ ...acc, [q.type]: (acc[q.type] || 0) + 1 }),
      { yes_no: 0, single: 0, multiple: 0, short_text: 0, long_text: 0 }
    );
    return { total, required, byType };
  }, [questions]);

  return (
    <CardShell
      title="Quick Questions"
      description="Questions that candidates will see when applying"
    >
      {/* Empty state */}
      {questions.length === 0 ? (
        <div className="text-center">
          <div className="mb-2 text-4xl">📝</div>
          <div className="text-sm text-gray-500">
            No quick questions configured for this job yet.
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-3 md:grid-cols-5">
            <StatBox label="Total" value={stat.total} />
            <StatBox label="Required" value={stat.required} />
            <StatBox label="Yes-No" value={stat.byType.yes_no} />
            <StatBox
              label="Single/Multiple"
              value={`${stat.byType.single}/${stat.byType.multiple}`}
            />
            <StatBox
              label="Text"
              value={`${stat.byType.short_text + stat.byType.long_text}`}
            />
          </div>

          {/* List items */}
          <div className="divide-y divide-[#c3c3c3]">
            {questions.map((q, i) => (
              <div key={q.id} className="grid gap-3 p-4 sm:grid-cols-12">
                <div className="sm:col-span-7">
                  <div className="mb-2 flex items-center gap-2">
                    <span className="">{i + 1}.</span>
                    <span className="">{q.text}</span>
                  </div>
                  <div className="flex flex-wrap items-center gap-2 text-xs">
                    <Badge
                      variant="outline"
                      size="sm"
                      className={cn(q.required ? 'text-rose-600' : '')}
                    >
                      {q.required ? 'Required' : 'Optional'}
                    </Badge>
                    <Badge variant="outline" size="sm">
                      Type: {TYPE_LABEL[q.type]}
                    </Badge>
                    {(q.type === 'short_text' || q.type === 'long_text') && (
                      <>
                        {typeof q.minLength === 'number' && (
                          <Badge variant="outline" size="sm">
                            Min {q.minLength}
                          </Badge>
                        )}
                        {typeof q.maxLength === 'number' && (
                          <Badge variant="outline" size="sm">
                            Max {q.maxLength}
                          </Badge>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Options / meta */}
                <div className="sm:col-span-5">
                  {(q.type === 'single' || q.type === 'multiple') && (
                    <div className="flex flex-wrap gap-2">
                      {q.options?.length ? (
                        q.options.map((o) => (
                          <Badge variant="outline" size="md" key={o.value}>
                            {o.value}
                          </Badge>
                        ))
                      ) : (
                        <Badge variant="outline" size="md">
                          No options yet
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </CardShell>
  );
}

function CopyButton({
  text,
  label = 'Copy',
}: {
  text: string;
  label?: string;
}) {
  const [ok, setOk] = useState(false);
  async function copy() {
    try {
      await navigator.clipboard.writeText(text);
      setOk(true);
      setTimeout(() => setOk(false), 1000);
    } catch {}
  }
  return (
    <button
      onClick={copy}
      className="rounded-lg border border-gray-300 px-2 py-1 text-[11px] text-gray-700 transition hover:bg-gray-50 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800"
    >
      {ok ? 'Copied' : label}
    </button>
  );
}

// ========= Demo page (shows the card exactly as it will look in Job Detail) =========

interface IProps {
  quickQuestions: JobQuickQuestion[];
}
export default function QuickQuestion({ quickQuestions }: IProps) {
  return (
    <div className="space-y-6">
      {/* Your page likely has other cards (Job Summary, About, Simulation). Here we only render the new card. */}
      <JobDetailQuickQuestionsCard questions={quickQuestions} />
    </div>
  );
}
