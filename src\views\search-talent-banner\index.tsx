'use client';

import { Button, Input, Select } from 'rizzui';
import SearchIcon from '../icons/search';
import { useAtom } from 'jotai';
import {
  searchModeAtom,
  searchTextAtom,
  selectedJobAtom,
} from '@/store/talent-atom';
import { useEffect, useState } from 'react';
import PageHeader from '@/shared/page-header';
import { useListOrgJob } from '@/api-requests/job/get-org-jobs';
import { orgAtom } from '@/store/organization-atom';
import { Job } from '@/api-requests/job';
import {
  renderDisplayValue,
  renderOptionDisplayValue,
} from '@/views/talent/shortlist-to-job-modal';
import { SelectOption } from '@/api-requests/types';

export default function SearchTalentBanner() {
  const [, setSearchModeTalent] = useAtom(searchModeAtom);
  const [, setSearchText] = useAtom(searchTextAtom);
  const [selectedJob, setSelectedJob] = useAtom(selectedJobAtom);
  const [org] = useAtom(orgAtom);

  const [jobOptions, setJobOptions] = useState<SelectOption[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [title, setTitle] = useState<string>('');

  const { data: jobData, isLoading: isLoadingJob } = useListOrgJob({
    orgId: org?._id as string,
    page: 1,
    limit: 100,
    title: title,
  });

  useEffect(() => {
    if (jobData && jobData.data) {
      const options = jobData.data.map((job) => ({
        label: job.title,
        value: job.jobId,
        companyLogoUrl: job.companyLogoUrl,
        companyName: job.companyName,
      }));
      setJobOptions(options);
    } else {
      setJobOptions([]);
    }
  }, [jobData]);

  useEffect(() => {
    return () => {
      setSearchText('');
      setInputValue('');
      setTitle('');
      setSelectedJob(null);
    };
  }, []);

  const handleSearch = () => {
    setSearchText(inputValue.trim());
    setSearchModeTalent(true);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Talent Search"
        description={
          <>
            <div>
              Allows employers to discover and evaluate candidates based on
              their Job Simulation results.
            </div>
            <div>
              Provides advanced filters, quick comparison, and direct actions
              (view profile, send job, message, shortlist).
            </div>
          </>
        }
        action={<></>}
      />
      <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
        <div className="w-full sm:w-[70%]">
          <Input
            variant="flat"
            prefix={<SearchIcon className="h-5 w-5 text-gray-500" />}
            suffix={
              <Button
                className="bg-primary text-white"
                size="sm"
                onClick={handleSearch}
              >
                Search Candidate
              </Button>
            }
            placeholder="Search by location, skill, industry"
            className="w-full min-w-[200px]"
            size="lg"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
          />
        </div>

        <div className="w-full sm:w-[30%] sm:min-w-[200px]">
          <Select
            clearable
            onClear={() => {
              setSelectedJob(null);
              setTitle('');
            }}
            options={jobOptions}
            placeholder="Select a job"
            className="w-full"
            searchable={true}
            onSearchChange={(val) => {
              setTitle(val);
            }}
            disableDefaultFilter={true}
            displayValue={(value: SelectOption & Job) =>
              renderDisplayValue(value)
            }
            getOptionDisplayValue={(option: SelectOption & Job) =>
              renderOptionDisplayValue(option)
            }
            value={selectedJob}
            onChange={(val: SelectOption & Job) => {
              setSelectedJob(val);
            }}
            size="lg"
          />
        </div>
      </div>
    </div>
  );
}
