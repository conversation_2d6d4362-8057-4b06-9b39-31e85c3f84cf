'use client';

import Field<PERSON>abel from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import { Input, Button, Text, Password } from 'rizzui';
import { ActionType } from './sign-in-modal';
import { Role } from '@/api-requests/user/types';
import { useAuthActions } from '@/hooks/use-auth-actions';
import toast from 'react-hot-toast';

type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  newPassword?: string;
};

interface IProps {
  onSetAction: (action: string) => void;
  onVerifyType: (action: string) => void;
  role: string;
}

export default function SignUpForm({
  onSetAction,
  onVerifyType,
  role,
}: IProps) {
  const { signupRequestCode } = useAuthActions();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const onSubmit = async (data: FormValues) => {
    if (data.password !== data.newPassword) {
      toast.error('Passwords do not match');
      return;
    }
    if (
      [Role.EMPLOYER, Role.COMMUNITY, Role.EDUCATION].includes(role as Role)
    ) {
      onSetAction(ActionType.COMPANY_SIGN_UP);
      return;
    }

    const resp = await signupRequestCode({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      role: Role.USER,
    });

    if (resp?.status === true) {
      onSetAction(ActionType.VERIFY_CODE);
      onVerifyType(ActionType.SIGN_UP);
    } else {
      toast.error(resp?.message || 'Failed to send verification code');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <div className="font-bold">
          {role === Role.USER
            ? 'Sign Up and Start Your Journey'
            : 'Register as an Employer'}
        </div>
        <div className="text-sm text-[#484848]">
          {role === Role.USER
            ? 'Enter your email, receive a login code, and explore the career path that’s waiting for you.'
            : 'Post jobs and connect with top talent.'}
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <FieldLabel title="First name" />
          <Controller
            name="firstName"
            control={control}
            rules={{
              required: 'Please enter first name',
              maxLength: {
                value: 24,
                message: 'First name cannot exceed 24 characters',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                variant="flat"
                placeholder="Enter your first name"
                className="w-full"
              />
            )}
          />
          {errors.firstName && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.firstName.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Last name" />
          <Controller
            name="lastName"
            control={control}
            rules={{
              required: 'Please enter last name',
              maxLength: {
                value: 24,
                message: 'Last name cannot exceed 24 characters',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                variant="flat"
                placeholder="Enter your last name"
                className="w-full"
              />
            )}
          />
          {errors.lastName && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.lastName.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Email" />
          <Controller
            name="email"
            control={control}
            rules={{
              required: 'Please enter email',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i,
                message: 'Invalid email',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="flat"
                placeholder="<EMAIL>"
                className="w-full"
              />
            )}
          />
          {errors.email && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.email.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Password" />
          <Controller
            name="password"
            control={control}
            rules={{
              required: 'Please enter password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your password"
                className="w-full"
              />
            )}
          />
          {errors.password && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.password.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Confirm password" />
          <Controller
            name="newPassword"
            control={control}
            rules={{
              required: 'Please enter password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your password"
                className="w-full"
              />
            )}
          />
          {errors.newPassword && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.newPassword.message}
            </Text>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <Button
          type="submit"
          className="w-full bg-primary text-white"
          disabled={!isValid || isSubmitting}
          onClick={handleSubmit(onSubmit)}
          isLoading={isSubmitting}
        >
          {role === Role.USER ? 'Create Account' : 'Next'}
        </Button>

        <div className="text-sm">
          <span className="text-[#484848]">Already have an account?</span>{' '}
          <button
            className="italic text-primary underline underline-offset-2 hover:opacity-80"
            onClick={() => onSetAction('')}
          >
            Sign In
          </button>
        </div>
      </div>
    </div>
  );
}
