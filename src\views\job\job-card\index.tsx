'use client';

// import Image from 'next/image';
import { Job } from '@/api-requests/job';
import { Image } from '@/shared/image';
import cn from '@/utils/class-names';
import { safeFormatDistanceToNow } from '@/utils/date';
import ClockIcon from '@/views/icons/clock';
import HeartIcon from '@/views/icons/heart';
import HeartOutlineIcon from '@/views/icons/heart-outline';
import MoneyIcon from '@/views/icons/money';
import TechnologyIcon from '@/views/icons/technology';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import { Tooltip } from 'rizzui/tooltip';

const getJobLocation = (job: Job) => {
  let jobLocation = job?.location || '';
  if (!!job?.city || !!job?.country) {
    jobLocation = `${job.city}${!!job.city ? ', ' : ''}${job.country}`;
  }
  return jobLocation;
};

interface JobCardProps {
  job: Job;
  setSelectedJob: (job: Job) => void;
  selectedJob?: Job | null;
  onFavorite: (job: Job) => void;
  isLoading?: boolean;
}

export default function JobCard({
  job,
  setSelectedJob,
  selectedJob,
  onFavorite,
  isLoading,
}: JobCardProps) {
  const formattedDate = safeFormatDistanceToNow(new Date(job?.postedTime), {
    addSuffix: true,
  });

  const jobType = getJobTypeString(job);

  return (
    <div
      className={cn(
        'flex h-full flex-col justify-between rounded-2xl border-[1px] border-[#c3c3c3] bg-white p-4',
        job?.jobId === selectedJob?.jobId && 'border-[2px] border-primary'
      )}
      onClick={() => setSelectedJob(job)}
    >
      <div>
        {/* Title + Badge */}
        <div className="flex items-start justify-between gap-1">
          <div>
            <h2 className="text-md line-clamp-2 flex-1 font-semibold text-gray-800">
              {job?.title}
            </h2>
            <div className="mt-1 flex w-full flex-row gap-2">
              {job.simulation?.id && (
                <Tooltip
                  color="invert"
                  content={
                    <ul className="ml-2 list-disc text-left">
                      <li>
                        You can apply by taking a short simulation (skills
                        challenge).
                      </li>
                      <li>
                        Results are shared with the employer and count as your
                        application.
                      </li>
                    </ul>
                  }
                >
                  <span className="rounded-full border border-[#05F98F] bg-[#EDFFF1] px-2 py-0.5 text-[10px] font-medium text-primary">
                    Simulation Available
                  </span>
                </Tooltip>
              )}
              {job?.simulation?.level && (
                <Tooltip
                  color="invert"
                  content={
                    <ul className="ml-2 list-disc text-left">
                      <li>
                        Estimated time to complete for an average candidate.
                      </li>
                      <li>Actual time may vary based on your experience.</li>
                    </ul>
                  }
                >
                  <span
                    className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-2 py-0.5 text-[10px] font-medium`}
                  >
                    {simulationLevel[Number(job.simulation.level)]}:{' '}
                    <b>{job.simulation.minute} mins</b>
                  </span>
                </Tooltip>
              )}
            </div>
          </div>
          <div className="flex-0">
            <button
              className="group flex h-8 w-8 items-center justify-center rounded-[8px] bg-[#F9F9F9] hover:bg-primary hover:text-white"
              onClick={(e) => {
                e.stopPropagation();
                onFavorite(job);
              }}
              disabled={isLoading}
            >
              {job?.interaction?.value ? (
                <HeartIcon className="h-5 w-5 group-hover:text-white" />
              ) : (
                <HeartOutlineIcon className="h-5 w-5 group-hover:text-white" />
              )}
            </button>
          </div>
        </div>

        <div className="mt-3 border-t border-[#c3c3c3]"></div>

        {/* Company Info */}
        <div className="mt-3 flex items-center gap-3">
          <div className="flex h-[60px] w-[60px] items-center justify-center overflow-hidden">
            <Image
              key={job?.jobId}
              src={job?.companyLogoUrl}
              alt={job?.companyName || 'Company Logo'}
              fallbackSrc="/org/default-logo.png"
              width={60}
              height={60}
              className="!h-full w-auto object-contain"
              loader={({ src }) => src}
            />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700">
              {job?.companyName}
            </p>
            <p className="text-[12px] text-gray-500">
              {getJobLocation(job)} • {formattedDate}
            </p>
          </div>
        </div>

        {/* Job Details */}
        <div className="mt-4 flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
          {!!job?.salary && (
            <span className="flex items-center gap-1">
              <MoneyIcon className="h-3 w-3" />
              <span>
                {typeof job?.salary === 'string'
                  ? job.salary
                  : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
              </span>
            </span>
          )}
          <span className="flex items-center gap-1">
            <ClockIcon className="h-3 w-3" />
            <span>{jobType || '-'}</span>
          </span>
          <span className="flex items-center gap-1">
            <TechnologyIcon className="h-3 w-3" />
            {Array.isArray(job?.categories) && job?.categories?.length > 0 ? (
              <span>{job.categories.join(', ')}</span>
            ) : (
              '-'
            )}
          </span>
          {/* {job?.simulation?.level && (
            <span
              className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-1`}
            >
              {simulationLevel[Number(job.simulation.level)]}:{' '}
              <b>{job.simulation.minute} mins</b>
            </span>
          )} */}
        </div>
      </div>

      <p
        className="mt-3 line-clamp-2 text-sm text-gray-600"
        dangerouslySetInnerHTML={{ __html: job.description || '' }}
      ></p>

      {/* Footer */}
      {/* <div>
        <div className="mt-3 border-t border-gray-200"></div>
        <div className="mt-4 flex items-center gap-4">
          <Button
            rounded="lg"
            className={cn(
              job?.jobId === selectedJob?.jobId
                ? 'bg-primary text-white'
                : 'border border-primary bg-white text-primary hover:bg-primary hover:text-white'
            )}
            size="sm"
            onClick={() => setSelectedJob(job)}
          >
            View detail
          </Button>
          <Button
            variant="outline"
            rounded="lg"
            className="flex items-center gap-1"
            size="sm"
          >
            <HeartOutlineIcon className="h-4 w-4" /> <span>Save</span>
          </Button>
          <div className="text-[12px] text-gray-500">
            <b>{job?.applicants}</b> people applied
          </div>
        </div>
      </div> */}
    </div>
  );
}
