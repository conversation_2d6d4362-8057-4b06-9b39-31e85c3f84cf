import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ShortlistCandidateQueryKeys,
  ShortlistCandidate,
  CreateManualParams,
} from './types';

export async function createShortlistCandidateManual(
  payload: CreateManualParams
): Promise<ShortlistCandidate> {
  const reps = await axiosInstance.post<ShortlistCandidate>(
    API_ENDPONTS.CREATE_SHORTLIST_CANDIDATE_MANUAL,
    payload
  );
  return reps.data;
}

export const useCreateShortlistCandidateManual = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateManualParams) =>
      createShortlistCandidateManual(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistCandidateQueryKeys.CREATE_SHORTLIST_CANDIDATE_MANUAL],
      });
    },
  });
};
