'use client';

import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { SelectOption } from '@/api-requests/types';
import DateRangePicker from '@/shared/date-range-picker';
import SearchIcon from '@/views/icons/search';
import { useState } from 'react';
import { ActionIcon, Input, InputProps, Select, Tooltip } from 'rizzui';
import HeartOutlineIcon from '../icons/heart-outline';
import JobAutoComplete from './job-autocomplete';

// const statusOptions: SelectOption[] = Object.values(ApplicationStatus).map(
//   (status) => ({
//     label: `Status: ${status.charAt(0).toUpperCase() + status.slice(1)}`,
//     value: status,
//   })
// );

const applicationStatusOptions: SelectOption[] = [
  { label: 'Draft', value: ApplicationStatus.DRAFT },
  { label: 'Submitted', value: ApplicationStatus.SUBMITTED },
  { label: 'Under Review', value: ApplicationStatus.UNDER_REVIEW },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  { label: 'Withdrawn', value: ApplicationStatus.WITHDRAWN },
  { label: 'Hired', value: ApplicationStatus.HIRED },
  { label: 'Closed', value: ApplicationStatus.CLOSED },
  // { label: 'Offer Accepted', value: ApplicationStatus.OFFER_ACCEPTED },
  // { label: 'Offer Declined', value: ApplicationStatus.OFFER_DECLINED },
];

const applyMethodOptions: SelectOption[] = [
  { label: 'Apply: CV', value: 'cv' },
  { label: 'Apply: Simulation', value: 'simulation' },
];

const sortOptions: SelectOption[] = [
  { label: 'Applied (Newest)', value: 'appliedAt:desc' },
  { label: 'Applied (Oldest)', value: 'appliedAt:asc' },
  { label: 'Match (Highest)', value: 'matchPercentage:desc' },
  { label: 'Match (Lowest)', value: 'matchPercentage:asc' },
];

const rowsPerPageOptions: SelectOption[] = [
  { label: '10', value: '10' },
  { label: '20', value: '20' },
  { label: '50', value: '50' },
];

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  selectedCandidates: ShortlistCandidate[];
  totalCandidates: number;
  sortDefaultValue: string | null;
  currentJob?: SelectOption | null;
  onHeartClick: () => void;
  onSelectJob: (jobId: string | null) => void;
  onSelectStatus: (value: string | null) => void;
  onSelectApplyMethod: (value: string | null) => void;
  onSelectSort: (value: string | null) => void;
  onChangeAppliedDateRange: (value: Date[] | null) => void;
}

export default function CandidatesFilter({
  searchProps,
  selectedCandidates,
  totalCandidates,
  sortDefaultValue,
  currentJob,
  onHeartClick,
  onSelectJob,
  onSelectStatus,
  onSelectApplyMethod,
  onSelectSort,
  onChangeAppliedDateRange,
}: IProps) {
  const [selectedStatus, setSelectedStatus] = useState<SelectOption | null>(
    null
  );
  const [selectedApplyMethod, setSelectedApplyMethod] =
    useState<SelectOption | null>(null);
  const [selectedSort, setSelectedSort] = useState<SelectOption | null>(
    sortOptions.find((option) => option.value === sortDefaultValue) || null
  );

  const handleSelectStatus = (option: SelectOption | null) => {
    setSelectedStatus(option);
    onSelectStatus(option?.value || null);
  };
  const handleSelectApplyMethod = (option: SelectOption | null) => {
    setSelectedApplyMethod(option);
    onSelectApplyMethod(option?.value || null);
  };
  const handleSelectSort = (option: SelectOption | null) => {
    setSelectedSort(option);
    onSelectSort(option?.value || null);
  };

  return (
    <div className="flex flex-col gap-3">
      {/* <div className="flex w-full flex-nowrap items-center gap-3 sm:gap-4">
        <div className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-500">
          {selectedCandidates.length > 0 && (
            <>
              <div className="font-medium text-gray-900">
                {selectedCandidates.length} selected
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <Tooltip color="invert" content="Add to shortlist">
                <ActionIcon
                  variant="text"
                  size="sm"
                  className="h-fit w-fit"
                  onClick={onHeartClick}
                >
                  <HeartOutlineIcon className="h-5 w-5 text-gray-500 hover:text-red-500" />
                </ActionIcon>
              </Tooltip>
            </>
          )}
        </div>
      </div> */}

      <div className="flex flex-wrap gap-6">
        <div className="flex flex-row gap-2">
          <Input
            prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
            className="min-w-[250px]"
            placeholder="Search candidate name, email"
            {...searchProps}
          />
        </div>
        {/* Filter container */}
        <div className="flex flex-col gap-1 md:flex-row md:items-center">
          <div className="whitespace-nowrap text-sm opacity-50">Filter:</div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <div className="w-60">
              <JobAutoComplete
                currentJob={currentJob}
                onSelectJob={onSelectJob}
              />
            </div>
            <Select
              clearable
              options={applicationStatusOptions}
              placeholder="All"
              className="w-full min-w-[220px] sm:w-auto"
              value={selectedStatus}
              onChange={handleSelectStatus}
              prefix="Status: "
              onClear={() => handleSelectStatus(null)}
            />
            <Select
              clearable
              options={applyMethodOptions}
              placeholder="Apply method: All"
              className="w-full min-w-[182px] sm:w-auto"
              value={selectedApplyMethod}
              onChange={handleSelectApplyMethod}
              onClear={() => handleSelectApplyMethod(null)}
            />
            <DateRangePicker
              placeholder="Applied date: Last 6 months"
              onConfirm={(dates) => onChangeAppliedDateRange(dates as Date[])}
              onClear={() => onChangeAppliedDateRange(null)}
              disableFuture
              dateRange={180}
              triggerClassName="bg-white !border !border-[#c3c3c3] !outline-1 !outline-[#c3c3c3] !focus:outline-1 !focus:outline-primary"
              prefixLabel="Applied date: "
              TriggerComponent={({ children, ...props }) => (
                <button
                  className="outline-solid h-10 rounded-xl border border-solid border-[#c3c3c3] bg-white px-3 py-2 outline outline-1 outline-[#c3c3c3] transition-all hover:outline-primary focus:outline focus:outline-primary"
                  {...props}
                >
                  {children}
                </button>
              )}
              // className="!outline-1 !outline-[#c3c3c3] !focus:outline-1 !focus:outline-primary"
            />
          </div>
        </div>
        <div className="flex flex-col gap-1 md:flex-row md:items-center 2xl:ml-auto">
          <div className="whitespace-nowrap text-sm opacity-50">Sort:</div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <Select
              clearable
              options={sortOptions}
              placeholder="Sort by applied date, match"
              className="w-full min-w-[200px] sm:w-auto"
              size="md"
              value={selectedSort}
              onChange={handleSelectSort}
              onClear={(e) => {
                e.stopPropagation();
                e.preventDefault();
                if (selectedSort?.value === 'appliedAt:desc') return;

                handleSelectSort(
                  sortOptions.find(
                    (option) => option.value === 'appliedAt:desc'
                  ) || null
                );
              }}
            />
          </div>
        </div>

        {/* <div className="ml-auto flex min-w-20 flex-col items-center justify-center gap-1">
          <div className="whitespace-nowrap text-sm opacity-50">
            Bulk Action
          </div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <Dropdown>
              <Dropdown.Trigger disabled={!selectedCandidates.length}>
                <Button
                  as="span"
                  variant="outline"
                  disabled={!selectedCandidates.length}
                  rounded="lg"
                >
                  Action {`(${selectedCandidates.length})`}
                </Button>
              </Dropdown.Trigger>
              <Dropdown.Menu>
                <Dropdown.Item>Add to Shortlist</Dropdown.Item>
                <Dropdown.Item>Compare</Dropdown.Item>
                <Dropdown.Item>Export Data</Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div> */}
      </div>
      <div className="flex w-full flex-nowrap items-center gap-3 sm:gap-4">
        <div className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-500">
          {selectedCandidates.length > 0 && (
            <>
              <div className="font-medium text-gray-900">
                {selectedCandidates.length} selected
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <Tooltip color="invert" content="Add to shortlist">
                <ActionIcon
                  variant="text"
                  size="sm"
                  className="h-fit w-fit"
                  onClick={onHeartClick}
                >
                  <HeartOutlineIcon className="h-5 w-5 text-gray-500 hover:text-red-500" />
                </ActionIcon>
              </Tooltip>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
