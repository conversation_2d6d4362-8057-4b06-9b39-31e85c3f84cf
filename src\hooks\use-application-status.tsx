import { ApplicationStatus } from '@/api-requests/job-candidate/types';

const applicationBadgeClasses: Record<string, string> = {
  draft: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300', // for incomplete sumulation
  submitted: 'bg-[#FED7AA] text-[#9A3412] ring-1 ring-[#F97316]',
  under_review: 'bg-sky-100 text-sky-800 ring-1 ring-sky-200',
  interview: 'bg-[#BBF7D0] text-[#166534] ring-1 ring-[#22C55E]',
  offer: 'bg-[#BFDBFE] text-[#1E3A8A] ring-1 ring-[#3B82F6]',
  rejected: 'bg-[#FECACA] text-[#7F1D1D] ring-1 ring-[#EF4444]',
  withdrawn: 'bg-primary text-white ring-1 ring-primary',
  hired: 'bg-primary text-white ring-1 ring-primary',
  closed: 'bg-primary text-white ring-1 ring-primary',
};

const useApplicationStatus = (status?: ApplicationStatus) => {
  const getApplicationStatusClassName = (status: ApplicationStatus) => {
    return (
      applicationBadgeClasses[status] ||
      'bg-stone-200 text-stone-800 ring-1 ring-stone-300'
    );
  };

  return {
    classes: status ? getApplicationStatusClassName(status) : '',
    getApplicationStatusClassName,
  };
};

export default useApplicationStatus;
