'use client';

import CloseIcon from '@/views/icons/close';
import FieldLabel from '@/views/job-creation/field-label';
import { useState } from 'react';
import { ActionIcon, Button, Input, Modal, Title } from 'rizzui';

interface IProps {
  open: boolean;
  isUpdating: boolean;
  onClose: () => void;
  onSave: (data: string) => Promise<boolean>;
}

export default function SkillModal({
  open,
  isUpdating,
  onClose,
  onSave,
}: IProps) {
  const [value, setValue] = useState('');

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">Add Skill</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            <FieldLabel title="Skill Name" />
            <Input
              variant="flat"
              placeholder="Enter your skill name"
              className="w-full"
              value={value}
              onChange={(e) => setValue(e.target.value)}
            />
          </div>

          {/* <div>
            <FieldLabel title="Proficiency Level" />
            <Input
              variant="flat"
              placeholder="Enter your proficiency level"
              className="w-full"
            />
          </div>

          <div className="w-full">
            <FieldLabel title="Description" />
            <Textarea
              variant="flat"
              placeholder="Enter your description"
              className="w-full"
            />
          </div>

          <div>
            <FieldLabel title="Proofs of skill" />
            <Input
              variant="flat"
              placeholder="Enter your proofs of skill"
              className="w-full"
            />
          </div> */}

          <div className="mt-6 flex justify-end gap-3">
            <Button
              className="bg-primary text-white"
              onClick={async () => {
                if (await onSave(value)) {
                  onClose();
                }
              }}
              disabled={isUpdating}
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
