import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { HomeSimulation, SimulationQueryKeys } from './types';

import { useQuery } from '@tanstack/react-query';

async function getPublicSimulation(params: {
  simId: string;
}): Promise<HomeSimulation> {
  const { simId } = params;
  const response = await requestGet<HomeSimulation>(
    API_ENDPONTS.GET_ORG_PUBLIC_SIMULATION,
    {},
    { simId }
  );
  return response.data;
}

export function useGetPublicSimulation(params: { simId: string }) {
  return useQuery<HomeSimulation>({
    queryKey: [SimulationQueryKeys.GET_PUBLIC_SIMULATION, params.simId],
    queryFn: () => getPublicSimulation(params),
    enabled: !!params.simId,
  });
}
