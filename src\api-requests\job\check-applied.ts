import { API_ENDPONTS } from '@/config/endpoint';
import { userAtom } from '@/store/user-atom';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { JobQueryKeys } from './types';

export async function checkApplied(
  jobId?: string,
  simulationId?: string
): Promise<boolean> {
  const response = await axiosInstance.get<{ applied: boolean }>(
    API_ENDPONTS.CHECK_APPLIED,
    {
      params: {
        jobId,
        simulationId,
      },
    }
  );
  return !!response.data.applied;
}

export function useCheckApplied(jobId?: string, simulationId?: string) {
  const [user] = useAtom(userAtom);

  return useQuery<boolean>({
    queryKey: [JobQueryKeys.CHECK_APPLIED, jobId, simulationId],
    queryFn: () => checkApplied(jobId, simulationId),
    enabled: !!user && !!jobId && !!simulationId,
  });
}
