'use client';

import { useMemo, useState } from 'react';

import UpIcon from '@/views/icons/up';
import CloseIcon from '@/views/icons/close';
import cn from '@/utils/class-names';
import { Checkbox, Accordion, Input, Select } from 'rizzui';
import countriesData from '@/data/countries.json';
import { SelectOption } from '@/api-requests/types';
import { debounce } from 'lodash';

const filterOptions = {
  matchScore: [
    { label: '90–100% (Highly matched)', value: '90-100' },
    { label: '75–89% (Good match)', value: '75-89' },
    { label: '< 75% (Low match)', value: '<75' },
  ],
  simulation: [
    { label: 'Completed', value: 'completed' },
    { label: 'Not Started', value: 'not_started' },
    { label: 'In Progress', value: 'in_progress' },
  ],
  level: [
    { label: 'Internship', value: 'Internship' },
    { label: 'Fresher', value: 'Fresher' },
    { label: 'Junior', value: 'Junior' },
    { label: 'Middle', value: 'Middle' },
    { label: 'Senior', value: 'Senior' },
    { label: 'Lead', value: 'Lead' },
    { label: 'Principal Engineer', value: 'Principal Engineer' },
    { label: 'Engineering Manager', value: 'Engineering Manager' },
    { label: 'Director of Engineering', value: 'Director of Engineering' },
    { label: 'CTO', value: 'CTO' },
  ],
  experience: [
    { label: 'Entry (0–2 years)', value: '0-2' },
    { label: 'Mid (3–5 years)', value: '3-5' },
    { label: 'Senior (6–9 years)', value: '6-9' },
    { label: 'Expert (10+ years)', value: '10+' },
  ],
  industry: [
    { label: 'Software Development', value: 'Software Development' },
    { label: 'Web Development', value: 'Web Development' },
    { label: 'Mobile App Development', value: 'Mobile App Development' },
    { label: 'Game Development', value: 'Game Development' },
    {
      label: 'DevOps & Cloud Engineering',
      value: 'DevOps & Cloud Engineering',
    },
    {
      label: 'Data Science & Machine Learning',
      value: 'Data Science & Machine Learning',
    },
    { label: 'Artificial Intelligence', value: 'Artificial Intelligence' },
    { label: 'Cybersecurity', value: 'Cybersecurity' },
    { label: 'Blockchain & Web3', value: 'Blockchain & Web3' },
    { label: 'UI/UX Design', value: 'UI/UX Design' },
    { label: 'Product Management', value: 'Product Management' },
    { label: 'IT Project Management', value: 'IT Project Management' },
    {
      label: 'Quality Assurance & Testing',
      value: 'Quality Assurance & Testing',
    },
    { label: 'Database Administration', value: 'Database Administration' },
    { label: 'Network Engineering', value: 'Network Engineering' },
    { label: 'Embedded Systems & IoT', value: 'Embedded Systems & IoT' },
    { label: 'AR/VR Development', value: 'AR/VR Development' },
    { label: 'IT Support & Helpdesk', value: 'IT Support & Helpdesk' },
    { label: 'Systems Administration', value: 'Systems Administration' },
    {
      label: 'Tech Writing & Documentation',
      value: 'Tech Writing & Documentation',
    },
  ],
  salary: [
    { label: '$0 – $5.000', value: '0-5000' },
    { label: '$5.000 – $10.000', value: '5000-10000' },
    { label: '$10.000 – $15.000', value: '10000-15000' },
    { label: '$15.000 – $20.000', value: '15000-20000' },
  ],
  workType: [
    { label: 'Full-time', value: 'fulltime' },
    { label: 'Part-time', value: 'parttime' },
    { label: 'Contract', value: 'contract' },
  ],
  workPlace: [
    { label: 'Onsite', value: 'onsite' },
    { label: 'Remote', value: 'remote' },
    { label: 'Hybrid', value: 'hybrid' },
  ],
};

const handleCheckboxChange = (
  value: string,
  currentValues: string[],
  setter: (values: string[]) => void
) => {
  const isChecked = currentValues.includes(value);
  if (isChecked) {
    setter(currentValues.filter((v) => v !== value));
  } else {
    setter([...currentValues, value]);
  }
};

const renderAccordion = (
  title: string,
  items: { label: string; value: string }[],
  currentValues: string[],
  setter: (values: string[]) => void
) => (
  <Accordion defaultOpen={true}>
    <Accordion.Header>
      {({ open }) => (
        <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
          <div>{title}</div>
          <UpIcon
            className={cn(
              'h-5 w-5 transform text-gray-500 transition-transform duration-300',
              open && 'rotate-180'
            )}
          />
        </div>
      )}
    </Accordion.Header>
    <Accordion.Body className="pl-6">
      <div className="space-y-2">
        {items.map(({ label, value }) => (
          <label key={value} className="flex items-center space-x-2">
            <Checkbox
              label={label}
              variant="flat"
              size="sm"
              value={value}
              checked={currentValues.includes(value)}
              onChange={() =>
                handleCheckboxChange(value, currentValues, setter)
              }
            />
          </label>
        ))}
      </div>
    </Accordion.Body>
  </Accordion>
);

interface IProps {
  skills: { label: string; value: string }[];
  setSkills: (skills: { label: string; value: string }[]) => void;
  levels: string[];
  setLevels: (levels: string[]) => void;
  industries: string[];
  setIndustries: (industries: string[]) => void;
  workTypes: string[];
  setWorkTypes: (workTypes: string[]) => void;
  workPlaces: string[];
  setWorkPlaces: (workPlaces: string[]) => void;
  experience: string[];
  setExperience: (experiences: string[]) => void;
  expectedSalary: { min: number; max: number } | null;
  setExpectedSalary: (salary: { min: number; max: number } | null) => void;
  country: SelectOption | null;
  setCountry: (country: SelectOption | null) => void;
}

export default function CandidateFilter({
  skills,
  setSkills,
  levels,
  setLevels,
  industries,
  setIndustries,
  workTypes,
  setWorkTypes,
  workPlaces,
  setWorkPlaces,
  experience,
  setExperience,
  expectedSalary,
  setExpectedSalary,
  country,
  setCountry,
}: IProps) {
  const [skillInput, setSkillInput] = useState('');

  const handleSkillKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && skillInput.trim() !== '') {
      e.preventDefault();
      const value = skillInput.trim();
      if (!skills.some((s) => s.value === value)) {
        setSkills([...skills, { label: value, value }]);
      }
      setSkillInput('');
    }
  };

  const removeSkill = (value: string) => {
    setSkills(skills.filter((s) => s.value !== value));
  };

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  const handleChangeSalary = debounce((key: 'min' | 'max', value: number) => {
    if (value === 0) {
      if (expectedSalary) {
        const newSalary = { ...expectedSalary };
        delete newSalary[key];

        if (Object.keys(newSalary).length === 0) {
          setExpectedSalary(null);
        } else {
          setExpectedSalary(newSalary);
        }
      }
      return;
    }

    if (expectedSalary) {
      setExpectedSalary({ ...expectedSalary, [key]: value });
    } else {
      setExpectedSalary({ [key]: value } as { min: number; max: number });
    }
  }, 500);

  return (
    <div className="col-span-12 h-fit space-y-6 rounded-2xl border border-[#c3c3c3] bg-white p-4 md:col-span-4 lg:col-span-3">
      {renderAccordion('Level', filterOptions.level, levels, setLevels)}
      {renderAccordion(
        'Experience',
        filterOptions.experience,
        experience,
        setExperience
      )}

      <Accordion defaultOpen={true}>
        <Accordion.Header>
          {({ open }) => (
            <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
              <div>Skills</div>
              <UpIcon
                className={cn(
                  'h-5 w-5 transform text-gray-500 transition-transform duration-300',
                  open && 'rotate-180'
                )}
              />
            </div>
          )}
        </Accordion.Header>
        <Accordion.Body>
          <div className="mt-2 flex flex-wrap gap-2">
            <Input
              className="w-full"
              placeholder="Add Skills"
              value={skillInput}
              onChange={(e) => setSkillInput(e.target.value)}
              onKeyDown={handleSkillKeyDown}
            />
            <div className="mt-2 flex flex-wrap gap-2">
              {skills.map((skill) => (
                <span
                  className="flex items-center gap-2 rounded-full border border-[#E1E1E1] bg-white px-2 py-1 text-[12px]"
                  key={skill.value}
                >
                  <span>{skill.label}</span>
                  <span
                    className="cursor-pointer rounded-full border border-[#E1E1E1] p-0.5 hover:bg-[#E1E1E1]"
                    onClick={() => removeSkill(skill.value)}
                  >
                    <CloseIcon className="h-3 w-3" />
                  </span>
                </span>
              ))}
            </div>
          </div>
        </Accordion.Body>
      </Accordion>

      <Accordion defaultOpen={true}>
        <Accordion.Header>
          {({ open }) => (
            <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
              <div>Location</div>
              <UpIcon
                className={cn(
                  'h-5 w-5 transform text-gray-500 transition-transform duration-300',
                  open && 'rotate-180'
                )}
              />
            </div>
          )}
        </Accordion.Header>
        <Accordion.Body>
          <div className="mt-2 flex flex-wrap gap-2">
            <Select
              clearable
              onClear={() => setCountry(null)}
              options={countryOptions}
              placeholder="Select your country"
              className="w-full"
              searchable={true}
              onChange={setCountry}
              value={country}
            />
            <div className="mt-2 flex flex-wrap gap-2">
              {skills.map((skill) => (
                <span
                  className="flex items-center gap-2 rounded-full border border-[#E1E1E1] bg-white px-2 py-1 text-[12px]"
                  key={skill.value}
                >
                  <span>{skill.label}</span>
                  <span
                    className="cursor-pointer rounded-full border border-[#E1E1E1] p-0.5 hover:bg-[#E1E1E1]"
                    onClick={() => removeSkill(skill.value)}
                  >
                    <CloseIcon className="h-3 w-3" />
                  </span>
                </span>
              ))}
            </div>
          </div>
        </Accordion.Body>
      </Accordion>

      {renderAccordion(
        'Industry',
        filterOptions.industry,
        industries,
        setIndustries
      )}
      {renderAccordion(
        'Work Type',
        filterOptions.workType,
        workTypes,
        setWorkTypes
      )}
      {renderAccordion(
        'Work Place',
        filterOptions.workPlace,
        workPlaces,
        setWorkPlaces
      )}
      {/* {renderAccordion(
        'Expected Salary (USD)',
        filterOptions.salary,
        expectedSalary,
        setExpectedSalary
      )} */}

      <Accordion defaultOpen={true}>
        <Accordion.Header>
          {({ open }) => (
            <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
              <div>Expected Salary (USD)</div>
              <UpIcon
                className={cn(
                  'h-5 w-5 transform text-gray-500 transition-transform duration-300',
                  open && 'rotate-180'
                )}
              />
            </div>
          )}
        </Accordion.Header>
        <Accordion.Body>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <Input
              variant="flat"
              placeholder="Min"
              className="w-full"
              inputClassName="rounded-lg"
              // value={expectedSalary?.min || ''}
              onChange={(e) => {
                const val = e.target.value || 0;
                if (val === '' || /^\d+$/.test(val as string)) {
                  handleChangeSalary('min', Number(val));
                }
              }}
            />
            <Input
              variant="flat"
              placeholder="Max"
              className="w-full"
              inputClassName="rounded-lg"
              // value={expectedSalary?.max || ''}
              onChange={(e) => {
                const val = e.target.value || 0;
                if (val === '' || /^\d+$/.test(val as string)) {
                  handleChangeSalary('max', Number(val));
                }
              }}
            />
          </div>
        </Accordion.Body>
      </Accordion>
    </div>
  );
}
