'use client';

import { Job } from '@/api-requests/job';
import { useListOrgJob } from '@/api-requests/job/get-org-jobs';
import { useGetShorlistByOrg } from '@/api-requests/shortlist';
import { SelectOption } from '@/api-requests/types';
import { User } from '@/api-requests/user';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  ActionIcon,
  Avatar,
  Button,
  Modal,
  Select,
  Text,
  Textarea,
  Title
} from 'rizzui';
import CloseIcon from '../icons/close';
import FieldLabel from '../job-creation/field-label';

type FormValues = {
  job: (SelectOption & Job) | null;
  shortlist: SelectOption | null;
  note: string;
};

export function renderDisplayValue(job: SelectOption & Job) {
  return (
    <span className="flex items-center gap-2">
      {/* <Avatar
        src={job.companyLogoUrl}
        name={job.companyName}
        className="h-7 w-7 rounded-full object-cover"
      /> */}
      <Text>{job.label}</Text>
    </span>
  );
}

function renderLoadingSkeleton() {
  return (
    <div className="flex items-center gap-3">
      <div className="h-9 w-9 animate-pulse rounded bg-gray-200"></div>
      <div className="flex-1">
        <div className="mb-1 h-4 animate-pulse rounded bg-gray-200"></div>
        <div className="h-3 w-3/4 animate-pulse rounded bg-gray-200"></div>
      </div>
    </div>
  );
}

export function renderOptionDisplayValue(job: SelectOption & Job) {
  return (
    <div className="flex items-center gap-3">
      <Avatar
        src={job.companyLogoUrl}
        name={job.companyName}
        customSize={40}
        className="border border-gray-300 !bg-transparent"
      />
      <div>
        <Text className="text-sm text-gray-500">{job.companyName}</Text>
        <Text fontWeight="bold">{job.label}</Text>
      </div>
    </div>
  );
}

interface IProps {
  open: boolean;
  onClose: () => void;
  isLoading?: boolean;
  onConfirm: (
    payload: {
      jobId: string;
      note?: string;
      shortlistId: string;
      orgId: string;
    } & User
  ) => void;
  talent: User;
}

export default function ShortListToJobModal({
  open,
  onClose,
  isLoading,
  onConfirm,
  talent,
}: IProps) {
  const [org] = useAtom(orgAtom);
  const [title, setTitle] = useState<string>('');

  const [jobOptions, setJobOptions] = useState<SelectOption[]>([]);
  const [shortlistOptions, setShortlistOptions] = useState<SelectOption[]>([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      job: null,
      shortlist: null,
      note: '',
    },
  });

  const { data: jobData, isLoading: isLoadingJob } = useListOrgJob({
    orgId: org?._id as string,
    page: 1,
    limit: 100,
    title: title,
  });
  const { data: shortlists } = useGetShorlistByOrg(org?._id as string);

  useEffect(() => {
    if (jobData && jobData.data) {
      const options = jobData.data.map((job) => ({
        label: job.title,
        value: job.jobId,
        companyLogoUrl: job.companyLogoUrl,
        companyName: job.companyName,
      }));
      setJobOptions(options);
    } else {
      setJobOptions([]);
    }
  }, [jobData]);

  useEffect(() => {
    if (shortlists?.length) {
      const options = shortlists.map((s) => ({
        label: s.name,
        value: s._id,
      }));
      setShortlistOptions(options);
    } else {
      setShortlistOptions([]);
    }
  }, [shortlists]);

  const onSubmit = (data: FormValues) => {
    onConfirm({
      orgId: org?._id as string,
      jobId: data.job?.value as string,
      note: data.note,
      shortlistId: data.shortlist?.value as string,
      ...talent,
    });
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg" customSize={'450px'}>
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">Shortlist to Job</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div>
            <FieldLabel title="Shortlist" required />
            <Controller
              name="shortlist"
              control={control}
              rules={{
                required: 'Please select a shortlist',
              }}
              render={({ field }) => (
                <Select
                  {...field}
                  clearable
                  onClear={() => field.onChange(null)}
                  options={shortlistOptions}
                  placeholder="Select your shortlist"
                  className="w-full"
                  searchable={true}
                  // value={selectedShortlist}
                  // onChange={(val: SelectOption) => {
                  //   setSelectedShortlist(val);
                  //   setShortlistError('');
                  // }}
                />
              )}
            />
            {errors.shortlist && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.shortlist.message}
              </Text>
            )}
          </div>

          <div>
            <FieldLabel title="job" required />
            <Controller
              name="job"
              control={control}
              rules={{
                required: 'Please select a job',
              }}
              render={({ field }) => (
                <Select
                  {...field}
                  clearable
                  onClear={() => field.onChange(null)}
                  options={jobOptions}
                  placeholder="Select a job"
                  className="w-full"
                  searchable={true}
                  onSearchChange={(val) => {
                    setTitle(val);
                  }}
                  disableDefaultFilter={true}
                  displayValue={(value: SelectOption & Job) =>
                    renderDisplayValue(value)
                  }
                  getOptionDisplayValue={(option: SelectOption & Job) =>
                    renderOptionDisplayValue(option)
                  }
                />
              )}
            />
            {errors.job && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.job.message}
              </Text>
            )}
          </div>

          <div>
            <FieldLabel title="Note" />
            <Controller
              name="note"
              control={control}
              rules={{
                required: false,
              }}
              render={({ field }) => (
                <Textarea
                  {...field}
                  variant="flat"
                  placeholder="Add a note"
                  className="w-full"
                />
              )}
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-4">
          <Button
            variant="outline"
            className="border-primary text-primary hover:bg-primary hover:text-white"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="bg-primary text-white"
            onClick={handleSubmit(onSubmit)}
            isLoading={isLoading}
            disabled={isLoading}
          >
            Confirm
          </Button>
        </div>
      </div>
    </Modal>
  );
}
