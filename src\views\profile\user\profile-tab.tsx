'use client';

import { UserProfile } from '@/api-requests/user-profile';
import { useCallback } from 'react';
import AllApplication from './all-application';
import AllFavouriteJobs from './favourite-jobs';
import ProfileDetail from './profile-detail';
import UserCV from './user-cv';

interface ProfileTabsProps {
  activeTab: string;
  tabs: { name: string; hash: string }[];
  refetch?: () => void;
  profile: UserProfile | null;
}
export default function ProfileTab({
  activeTab,
  tabs,
  refetch,
  profile,
}: ProfileTabsProps) {
  const renderTabContent = useCallback(() => {
    switch (activeTab) {
      case 'profile':
        return <ProfileDetail refetch={refetch} profile={profile} />;
      case 'cv':
        return <UserCV />;
      case 'applications':
        return <AllApplication />;
      case 'saved-jobs':
        return <AllFavouriteJobs />;
    }
  }, [activeTab, tabs, profile]);

  return <div>{renderTabContent()}</div>;
}
