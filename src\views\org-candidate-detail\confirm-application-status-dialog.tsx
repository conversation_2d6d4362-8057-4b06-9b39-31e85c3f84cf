'use client';

import { Button, Modal } from 'rizzui';

interface IProps {
  open: boolean;
  currentStatus: string;
  newStatus: string;
  setOpen: (open: boolean) => void;
  onConfirm: () => void;
}

export default function ConfirmChangeStatusDialog({
  open,
  currentStatus,
  newStatus,
  setOpen,
  onConfirm,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={() => setOpen(false)}>
      <div className="flex flex-col items-center gap-5 p-6 text-center">
        <p className="text-lg font-bold text-[#484848]">Update Status</p>
        <p className="text-[16px]">
          You are updating the status from{' '}
          <span className="font-bold">{currentStatus}</span> to{' '}
          <span className="font-bold">{newStatus}</span>. Please confirm your
          decision.
        </p>
        <div className="flex gap-2">
          <Button onClick={() => setOpen(false)} variant="outline">
            <span>Cancel</span>
          </Button>
          <Button
            onClick={() => onConfirm?.()}
            variant="solid"
            className="text-white"
          >
            <span>Confirm</span>
          </Button>
        </div>
      </div>
    </Modal>
  );
}
