'use client';

import { Job } from '@/api-requests/job';
import { useGetCandidatesByJob } from '@/api-requests/job-candidate/get-candidate-by-job';
import { useGetCandidateDashboardStatsByJob } from '@/api-requests/job-candidate/get-candidate-dashboard-stats-by-job';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { LIMIT, SelectOption } from '@/api-requests/types';
import { LongTextCell } from '@/shared/long-text-cell';
import {
  convertApplicationStatus,
  convertSimulationStatus,
} from '@/utils/application-simulation-status';
import { safeFormatDate } from '@/utils/date';
import { debounce } from 'lodash';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { Avatar, Badge, Dropdown, Loader, Table } from 'rizzui';
import EditSquareIcon from '../icons/edit-square';
import Pagination from '../pagination';
import CandidateFilter from './candidate-filter';
import MoreHorizontalIcon from '../icons/more-horizontal';
import useApplicationStatus from '@/hooks/use-application-status';
import useSimulationStatus from '@/hooks/use-simulation-status';

// const simulationBadgeClasses: Record<string, string> = {
//   active: 'bg-[#FFE2C0] text-[#CD6B01]',
//   completed: 'bg-[#FFD8D8] text-[#B90707]',
// };

// const statusBadgeClasses: Record<string, string> = {
//   pending: 'bg-[#FFF4E5] text-[#B58105]',
//   screening: 'bg-[#E5F6FF] text-[#0278C2]',
//   interview: 'bg-[#E6FFEF] text-[#027A48]',
//   offer: 'bg-[#FFF4E5] text-[#B58105]',
//   rejected: 'bg-[#FFD8D8] text-[#B90707]',
//   withdrawn: 'bg-[#FFD8D8] text-[#B90707]',
//   hired: 'bg-[#E6FFEF] text-[#027A48]',
// };

interface IProps {
  jobId?: string;
  setCandidateInfo?: (info: {
    totalCandidates: number;
    matchedCandidates: number;
    averageMatch: number;
  }) => void;
  job: Job;
}

const parseApplyMode = (applyMode: string) => {
  if (applyMode === 'all') return 'CV, Simulation';
  if (applyMode === 'cv') return 'CV';
  if (applyMode === 'simulation') return 'Simulation';
  return '-';
};

export default function CandidateListTab({
  jobId,
  setCandidateInfo,
  job,
}: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const { getSimulationStatusClassName } = useSimulationStatus();

  const router = useRouter();
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState<string>('');
  const [applicationStatus, setApplicationStatus] =
    useState<SelectOption | null>(null);
  const [simulationStatus, setSimulationStatus] = useState<SelectOption | null>(
    null
  );

  const searchRef = useRef<HTMLInputElement>(null!);

  const { data: candidateData, isLoading } = useGetCandidatesByJob({
    jobId,
    page,
    limit: LIMIT,
    name: search,
    applicationStatus: applicationStatus?.value,
    simulationStatus: simulationStatus?.value,
  });

  const { data: candidateStats } = useGetCandidateDashboardStatsByJob(
    jobId || ''
  );

  useEffect(() => {
    if (setCandidateInfo && candidateData) {
      setCandidateInfo({
        totalCandidates: candidateData?.meta.total || 0,
        matchedCandidates: candidateData?.meta.total
          ? candidateData?.data?.filter(
              (c) => c.matchPercentage && c.matchPercentage >= 70
            ).length
          : 0,
        averageMatch: candidateData?.meta.total
          ? Math.round(
              candidateData?.data?.reduce(
                (sum, c) => sum + (c.matchPercentage || 0),
                0
              ) / candidateData?.meta.total
            )
          : 0,
      });
    }
  }, [candidateData]);

  const handleSearch = debounce((value: string) => {
    if (!value || value.trim().length > 2) {
      setSearch(value);
      setPage(1);
    }
  }, 500);

  const handleFilter = (option: SelectOption | null, type: string) => {
    if (type === 'applicationStatus') {
      setApplicationStatus(option);
    } else if (type === 'simulationStatus') {
      setSimulationStatus(option);
    }
    setPage(1);
  };

  const actions = [
    // {
    //   icon: <MessageIcon className="h-5 w-5" />,
    //   label: 'Message',
    // },
    // {
    //   icon: <DownloadCvIcon className="h-5 w-5" />,
    //   label: 'Download CV',
    // },
    {
      icon: <EditSquareIcon className="h-5 w-5" />,
      label: 'View Detail',
      onClick: (c: ShortlistCandidate) =>
        router.push('/org/admin/candidates/' + c._id),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-5">
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Total</div>
          <div className="text-lg font-bold">{job?.applicants || 0}</div>
        </div>
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Simulation done</div>
          <div className="text-lg font-bold">
            {candidateStats?.simulationDone || 0}
          </div>
        </div>
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">New (7d)</div>
          <div className="text-lg font-bold">{candidateStats?.new || 0}</div>
        </div>
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Interview</div>
          <div className="text-lg font-bold">
            {candidateStats?.interview || 0}
          </div>
        </div>
        <div className="rounded-2xl border border-[#c3c3c3] bg-white p-4">
          <div className="text-gray-500">Rejected</div>
          <div className="text-lg font-bold">
            {candidateStats?.rejected || 0}
          </div>
        </div>
      </div>

      <CandidateFilter
        searchProps={{
          onChange: (e) => handleSearch(e.target.value),
          ref: searchRef,
        }}
        statusProps={{
          value: applicationStatus,
          onClear: () => setApplicationStatus(null),
          onChange: (val) => handleFilter(val, 'applicationStatus'),
        }}
        simulationProps={{
          value: simulationStatus,
          onClear: () => setSimulationStatus(null),
          onChange: (val) => handleFilter(val, 'simulationStatus'),
        }}
      />

      <div className="overflow-x-auto rounded-xl bg-white pb-5">
        <Table>
          <Table.Header className="rounded-t-xl border-b border-[#c3c3c3] !bg-white">
            <Table.Row>
              {/* <Table.Head className="w-4"> </Table.Head> */}
              <Table.Head className="w-[20%]">Candidate name</Table.Head>
              <Table.Head className="w-[50px]">Matches</Table.Head>
              <Table.Head className="w-[20%]">Status</Table.Head>
              <Table.Head className="w-[20%]">Simulation</Table.Head>
              <Table.Head className="w-[10%]">Apply</Table.Head>
              <Table.Head className="w-full">AI Review</Table.Head>
              <Table.Head className="!text-right">Actions</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {candidateData?.data?.map((candidate) => (
              <Table.Row key={candidate._id} className="border-[#c3c3c3]">
                {/* <Table.Cell>
   <Checkbox variant="flat" size="sm" />
 </Table.Cell> */}

                <Table.Cell>
                  <div className="flex items-center gap-3">
                    <Avatar
                      src={candidate.user?.avatar || '/avatar/user-default.png'}
                      name={
                        candidate.user?.firstName +
                        ' ' +
                        candidate.user?.lastName
                      }
                      customSize={40}
                      className="!bg-transparent"
                    />
                    <div className="text-left">
                      <div className="font-medium text-gray-900">
                        {candidate.user?.firstName +
                          ' ' +
                          candidate.user?.lastName}
                      </div>
                      <div className="text-xs text-gray-500">
                        {candidate.email}
                      </div>
                    </div>
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <span className="font-semibold text-gray-800">
                    {candidate.matchPercentage || '--'}%
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={getApplicationStatusClassName(
                      candidate.applicationStatus || ''
                    )}
                  >
                    {convertApplicationStatus(
                      candidate.applicationStatus || ''
                    )}
                  </Badge>
                </Table.Cell>

                <Table.Cell>
                  <div>{candidate.simulation?.name}</div>
                  {!!candidate.simulationStatus && (
                    <Badge
                      variant="flat"
                      size="sm"
                      className={getSimulationStatusClassName(
                        candidate.simulationStatus
                      )}
                    >
                      {convertSimulationStatus(candidate.simulationStatus)}
                    </Badge>
                  )}
                </Table.Cell>

                <Table.Cell>
                  <div>
                    Method: {parseApplyMode(candidate?.applyMode as string)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Applied at:{' '}
                    {safeFormatDate(candidate?.appliedAt, {
                      fallback: '-',
                    })}
                  </div>
                </Table.Cell>

                <Table.Cell>
                  {/* <div>{candidate.aiEvaluation?.summary || '-'}</div> */}
                  <div>
                    <LongTextCell
                      text={candidate.aiEvaluation?.summary || ''}
                      lines={2}
                    />
                  </div>
                </Table.Cell>

                <Table.Cell className="text-right">
                  <div className="flex justify-end gap-3">
                    <Dropdown placement="bottom-end">
                      <Dropdown.Trigger>
                        <MoreHorizontalIcon />
                      </Dropdown.Trigger>
                      <Dropdown.Menu className="w-fit divide-y">
                        {actions.map((action, idx) => (
                          <Dropdown.Item
                            key={idx}
                            className="hover:bg-primary hover:text-white"
                            onClick={() => action.onClick?.(candidate)}
                          >
                            <div className="flex items-center">
                              {action.icon}
                              <span className="ml-2">{action.label}</span>
                            </div>
                          </Dropdown.Item>
                        ))}
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-40 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              candidateData?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No candidates found</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-[#c3c3c3] pt-4" />

        <Pagination
          total={candidateData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </div>
  );
}
