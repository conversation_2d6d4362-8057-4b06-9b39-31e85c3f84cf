'use client';

import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import useApplicationStatus from '@/hooks/use-application-status';
import useSimulationStatus from '@/hooks/use-simulation-status';
import { LongTextCell } from '@/shared/long-text-cell';
import {
  convertApplicationStatus,
  convertSimulationStatus,
} from '@/utils/application-simulation-status';
import { safeFormatDate } from '@/utils/date';
import EditSquareIcon from '@/views/icons/edit-square';
import { ChevronDownIcon, MoreHorizontalIcon } from 'lucide-react';
import {
  Accordion,
  Avatar,
  Badge,
  Checkbox,
  cn,
  Dropdown,
  Loader,
  Table,
  Tooltip,
} from 'rizzui';
import HeartOutlineIcon from '../icons/heart-outline';
import Pagination from '../pagination';

// TODO: move to utils
export const getApplyModeText = (mode: string) => {
  switch (mode) {
    case 'cv':
      return 'CV';
    case 'simulation':
      return 'Simulation';
    default:
      return '';
  }
};

interface IProps {
  jobCandidateData: ApiListResponse<ShortlistCandidate>;
  isLoading: boolean;
  page: number;
  selectedCandidates: ShortlistCandidate[];
  setPage: (page: number) => void;
  onClickAction: (action: string, candidate: ShortlistCandidate) => void;
  onSelectJob: (value: SelectOption) => void;
  onCheckboxChange: (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => void;
}

export default function CandidatesTable({
  jobCandidateData,
  isLoading,
  page,
  selectedCandidates,
  setPage,
  onClickAction,
  onCheckboxChange,
  onSelectJob,
}: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const { getSimulationStatusClassName } = useSimulationStatus();

  const actions = [
    // {
    //   icon: <MessageIcon className="h-5 w-5" />,
    //   label: 'Message',
    // },
    // {
    //   icon: <DownloadCvIcon className="h-5 w-5" />,
    //   label: 'Download CV',
    // },
    {
      icon: <HeartOutlineIcon className="h-5 w-5" />,
      label: 'Add to Shortlist',
    },
    {
      icon: <EditSquareIcon className="h-5 w-5" />,
      label: 'View Detail',
    },
  ];

  return (
    <div>
      <div className="rounded-xl bg-white pb-5">
        <div className="overflow-x-auto">
          <Table>
            <Table.Header className="rounded-t-xl border-b border-[#c3c3c3] !bg-white">
              <Table.Row>
                <Table.Head className="w-4">
                  <Checkbox
                    variant="flat"
                    size="sm"
                    checked={
                      selectedCandidates.length ===
                        jobCandidateData?.data?.length &&
                      jobCandidateData?.data?.length > 0
                    }
                    onChange={(e) => onCheckboxChange(e.target.checked, 'all')}
                  />
                </Table.Head>
                <Table.Head className="min-w-[200px] lg:w-[20%]">
                  Candidate name
                </Table.Head>
                <Table.Head className="">Match</Table.Head>
                <Table.Head className="min-w-[250px] lg:w-[20%]">
                  Job
                </Table.Head>
                <Table.Head className="w-auto lg:min-w-[110px]">
                  Status
                </Table.Head>
                <Table.Head className="w-auto">Applied</Table.Head>
                <Table.Head className="min-w-[350px] lg:w-auto">
                  AI Review
                </Table.Head>
                <Table.Head className="!text-right">Actions</Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {jobCandidateData?.data?.map((candidate, index) => (
                <Table.Row key={index} className="border-[#c3c3c3]">
                  <Table.Cell>
                    <Checkbox
                      variant="flat"
                      size="sm"
                      checked={selectedCandidates.some(
                        (c) => c._id === candidate._id
                      )}
                      onChange={(e) =>
                        onCheckboxChange(e.target.checked, candidate)
                      }
                    />
                  </Table.Cell>

                  <Table.Cell>
                    <div
                      className="flex cursor-pointer items-center gap-1"
                      onClick={() => {
                        onClickAction('View Detail', candidate);
                      }}
                    >
                      <Avatar
                        src={
                          candidate.user?.avatar || '/avatar/user-default.png'
                        }
                        name={
                          candidate.user?.firstName +
                          ' ' +
                          candidate.user?.lastName
                        }
                        customSize={50}
                        className="!bg-transparent"
                      />
                      <div
                        className="text-left"
                        style={{ wordBreak: 'break-word' }}
                      >
                        <div className="font-medium text-gray-900 hover:underline">
                          {candidate.user?.firstName +
                            ' ' +
                            candidate.user?.lastName}
                        </div>
                        <div className="text-xs text-gray-500">
                          {candidate.email}
                        </div>
                      </div>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <span className="text-sm font-semibold text-[#1D2D44]">
                      {candidate.matchPercentage !== undefined ? (
                        `${candidate.matchPercentage}%`
                      ) : (
                        <Tooltip
                          color="invert"
                          content="User has not completed the simulation"
                        >
                          <span>--%</span>
                        </Tooltip>
                      )}
                    </span>
                  </Table.Cell>

                  <Table.Cell>
                    <div className="text-sm">{candidate.job?.title || '-'}</div>
                    {!!candidate.otherJobs?.length && (
                      <div className="text-sm text-gray-500">
                        <Accordion className="">
                          <Accordion.Header>
                            {({ open }) => (
                              <div className="flex w-full cursor-pointer items-center justify-between text-sm font-semibold">
                                Other positions:
                                <ChevronDownIcon
                                  className={cn(
                                    'h-4 w-4 -rotate-90 transform transition-transform duration-300',
                                    open && '-rotate-0'
                                  )}
                                />
                              </div>
                            )}
                          </Accordion.Header>
                          <Accordion.Body className="">
                            <ul className="list-inside list-disc space-y-1 text-sm text-gray-500">
                              {candidate.otherJobs.map((otherJob) => (
                                <li
                                  key={otherJob.id}
                                  className="cursor-pointer hover:underline"
                                  onClick={() =>
                                    onSelectJob({
                                      label:
                                        otherJob.job?.title ||
                                        otherJob.simulation?.name ||
                                        '',
                                      value:
                                        otherJob.jobId ||
                                        otherJob.simulationId ||
                                        '',
                                    })
                                  }
                                >
                                  {otherJob.job?.title ||
                                    otherJob.simulation?.name ||
                                    '--'}
                                </li>
                              ))}
                            </ul>
                          </Accordion.Body>
                        </Accordion>
                      </div>
                    )}
                  </Table.Cell>

                  <Table.Cell>
                    <Badge
                      variant="flat"
                      size="sm"
                      className={
                        // applicationBadgeClasses[candidate.applicationStatus]
                        getApplicationStatusClassName(
                          candidate.applicationStatus
                        )
                      }
                    >
                      {convertApplicationStatus(candidate.applicationStatus)}
                    </Badge>
                  </Table.Cell>

                  <Table.Cell>
                    <div className="flex flex-col gap-1">
                      <p className="text-sm">
                        <Tooltip
                          color="invert"
                          content={safeFormatDate(candidate.appliedAt, {
                            format: 'full',
                          })}
                        >
                          <span className="inline space-x-1">
                            <span>
                              {safeFormatDate(candidate.appliedAt, {
                                format: 'short',
                              })}
                            </span>
                            <span>•</span>
                            <span>
                              {safeFormatDate(candidate.appliedAt, {
                                format: 'relative',
                              })}
                            </span>
                          </span>
                        </Tooltip>
                      </p>

                      <p className="flex gap-1 text-sm text-gray-500">
                        Method:{' '}
                        <Badge variant="outline" size="sm">
                          {getApplyModeText(candidate.applyMode)}
                        </Badge>
                        {(candidate.applyMode === 'simulation' ||
                          !!candidate.hasQuickQuestions) && <span>•</span>}
                        {candidate.applyMode === 'simulation' && (
                          <Badge
                            variant="flat"
                            size="sm"
                            className={getSimulationStatusClassName(
                              candidate.simulationStatus
                            )}
                          >
                            {convertSimulationStatus(
                              candidate.simulationStatus
                            )}
                          </Badge>
                        )}
                        {!!candidate.hasQuickQuestions && (
                          <Badge variant="outline" size="sm">
                            Questions
                          </Badge>
                        )}
                      </p>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <div>
                      <LongTextCell
                        text={candidate.aiEvaluation?.summary || ''}
                        lines={2}
                      />
                    </div>
                    {/* <ul className="list-inside list-disc space-y-1 text-sm text-gray-700">
                    {candidate.review.map((line, i) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul> */}
                  </Table.Cell>

                  <Table.Cell className="text-right">
                    <div className="flex justify-end gap-3">
                      <Dropdown placement="bottom-end">
                        <Dropdown.Trigger>
                          <MoreHorizontalIcon />
                        </Dropdown.Trigger>
                        <Dropdown.Menu className="w-fit divide-y">
                          {actions.map((action, idx) => (
                            <Dropdown.Item
                              key={idx}
                              className="hover:bg-primary hover:text-white"
                              onClick={() =>
                                onClickAction(action.label, candidate)
                              }
                            >
                              <div className="flex items-center">
                                {action.icon}
                                <span className="ml-2">{action.label}</span>
                              </div>
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}

              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={8} className="h-40 text-center">
                    <div className="flex min-h-40 items-center justify-center">
                      <Loader className="h-8 w-8" />
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                jobCandidateData?.data?.length === 0 && (
                  <Table.Row>
                    <Table.Cell colSpan={8} className="text-center">
                      <div className="text-gray-500">No candidates found</div>
                    </Table.Cell>
                  </Table.Row>
                )
              )}
            </Table.Body>
          </Table>
        </div>

        <hr className="border-t border-[#c3c3c3] pt-4" />

        <Pagination
          total={jobCandidateData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </div>
  );
}
