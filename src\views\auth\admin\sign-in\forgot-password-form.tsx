'use client';

import Field<PERSON>abe<PERSON> from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import { Button, Text, Password, Input } from 'rizzui';
import ArrowLeftIcon from '@/views/icons/arrow-left';
import { useAuthActions } from '@/hooks/use-auth-actions';
import toast from 'react-hot-toast';
import { ActionType } from '../../sign-in-up/sign-in-modal';

type FormValues = {
  email: string;
  newPassword: string;
  confirmPassword: string;
};

interface IProps {
  onSetAction: (action: string) => void;
  onVerifyType: (action: ActionType) => void;
}

export default function ForgotPasswordForm({
  onSetAction,
  onVerifyType,
}: IProps) {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const { forgotPasswordRequestCode } = useAuthActions();

  const onSubmit = async (data: FormValues) => {
    if (data.newPassword !== data.confirmPassword) {
      toast.error('New password and confirm new password do not match');
      return;
    }
    const resp = await forgotPasswordRequestCode(data);
    if (resp?.status === true) {
      onSetAction(ActionType.VERIFY_CODE);
      onVerifyType(ActionType.RESET_PASSWORD);
    } else {
      toast.error(resp?.message || 'Something went wrong');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <button onClick={() => onSetAction('')}>
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div className="mt-2 font-bold">Forgot Password</div>
        <div className="text-sm text-[#484848]">
          Please enter correct information to request a password change.
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <FieldLabel title="Email" />
          <Controller
            name="email"
            control={control}
            rules={{
              required: 'Please enter email',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i,
                message: 'Invalid email',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="flat"
                placeholder="<EMAIL>"
                className="w-full"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (isValid) {
                      e.preventDefault();
                      handleSubmit(onSubmit)();
                    }
                  }
                }}
              />
            )}
          />
          {errors.email && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.email.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="New password" />
          <Controller
            name="newPassword"
            control={control}
            rules={{
              required: 'Please enter new password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your new password"
                className="w-full"
              />
            )}
          />
          {errors.newPassword && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.newPassword.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Comfirm new password" />
          <Controller
            name="confirmPassword"
            control={control}
            rules={{
              required: 'Please enter confirm new password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your confirm new password"
                className="w-full"
              />
            )}
          />
          {errors.confirmPassword && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.confirmPassword.message}
            </Text>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <Button
          className="w-full bg-primary text-white"
          disabled={isSubmitting}
          isLoading={isSubmitting}
          onClick={handleSubmit(onSubmit)}
        >
          Send request
        </Button>
      </div>
    </div>
  );
}
