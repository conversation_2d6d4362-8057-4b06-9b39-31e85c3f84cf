'use client';

import { useUpdateCV } from '@/api-requests/user-profile/update-cv';
import { userAtom, UserCVEducation, UserCVExperience } from '@/store/user-atom';
import DeleteIcon from '@/views/icons/delete';
import EditIcon from '@/views/icons/edit';
import PlusIcon from '@/views/icons/plus';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useAtom } from 'jotai';
import { GripVertical } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { ActionIcon } from 'rizzui';
import AboutModal from './about-modal';
import EducationModal from './education-modal';
import ExperienceModal from './experience-modal';
import SkillModal from './skill-modal';

export const getMonthName = (humanMonth: number) => {
  return new Date(new Date().getFullYear(), humanMonth - 1).toLocaleString(
    'en-US',
    {
      month: 'short',
    }
  );
};

// TODO: drag and drop to sort skills, experiences, education
// TODO: add Enter key support for modals

export default function UserCV() {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const [aboutModal, setAboutModal] = useState(false);
  const [experienceModal, setExperienceModal] = useState(false);
  const [skillModal, setSkillModal] = useState(false);
  const [educationModal, setEducationModal] = useState(false);

  const [selectedData, setSelectedData] = useState<any>(undefined);

  const [user] = useAtom(userAtom);
  const cv = user?.profile?.cv;

  const [cvSkills, setCVSkills] = useState(cv?.skills || []);
  const [cvEducation, setCVEducation] = useState(cv?.educations || []);
  const [cvExperience, setCVExperience] = useState(cv?.experiences || []);

  const { mutateAsync: updateCV, isPending: isUpdating } = useUpdateCV();

  const handleUpdateAbout = async (data: string) => {
    if (isUpdating) return false;
    try {
      await updateCV({ id: cv?.id, type: 'about', data });
      toast.success('About updated successfully');
      return true;
    } catch (error) {
      toast.error('Failed to update about. Please try again later.');
      return false;
    }
  };

  const handleAddSkills = async (data: string) => {
    if (isUpdating || !data || !data.trim?.()?.length) return false;
    try {
      await updateCV({
        id: cv?.id,
        type: 'skills',
        data: [...(cv?.skills || []), { skill: data }],
      });
      toast.success('Skills updated successfully');
      return true;
    } catch (error) {
      toast.error('Failed to update skills. Please try again later.');
      return false;
    }
  };

  const handleDeleteSkill = async (id: string) => {
    console.log('Deleting skill with id:', id);
    if (isUpdating || !cv?.skills) return false;

    const foundSkill = cv?.skills?.find((skill) => skill.id === id);
    console.log('foundSkill ::: ', foundSkill);

    if (!foundSkill) return false;

    try {
      await updateCV({
        id: cv?.id,
        type: 'skills',
        data: (cv?.skills || []).filter((skill) => skill.id !== id),
      });
      toast.success('Skills updated successfully');
      return true;
    } catch (error) {
      toast.error('Failed to update skills. Please try again later.');
      return false;
    }
  };

  const handleUpdateExperience = async (data: UserCVExperience) => {
    if (isUpdating || !data) return false;

    let dataToUpdate: UserCVExperience[] = [];

    if (!data.id) {
      dataToUpdate = [...(cv?.experiences || []), data];
    } else {
      dataToUpdate = (cv?.experiences || [])?.map((item) => {
        if (item.id === data.id) {
          return { ...item, ...data };
        }
        return item;
      });
    }

    try {
      await updateCV({
        id: cv?.id,
        type: 'experiences',
        data: dataToUpdate,
      });
      toast.success('Experience updated successfully');
      return true;
    } catch (error) {
      toast.error('Failed to update Experience. Please try again later.');
      return false;
    }
  };

  const handleDeleteExperience = async (id: string) => {
    if (isUpdating || !cv?.experiences) return false;

    const foundItem = cv?.experiences?.find((item) => item.id === id);

    if (!foundItem) return false;

    try {
      await updateCV({
        id: cv?.id,
        type: 'experiences',
        data: (cv?.experiences || []).filter((item) => item.id !== id),
      });
      toast.success('Experience deleted successfully');
      return true;
    } catch (error) {
      toast.error('Failed to delete experience. Please try again later.');
      return false;
    }
  };

  const handleDeleteEducation = async (id: string) => {
    if (isUpdating || !cv?.educations) return false;

    const foundItem = cv?.educations?.find((item) => item.id === id);

    if (!foundItem) return false;

    try {
      await updateCV({
        id: cv?.id,
        type: 'educations',
        data: (cv?.educations || []).filter((item) => item.id !== id),
      });
      toast.success('Education deleted successfully');
      return true;
    } catch (error) {
      toast.error('Failed to delete education. Please try again later.');
      return false;
    }
  };

  const handleUpdateEducation = async (data: UserCVEducation) => {
    if (isUpdating || !data) return false;

    let dataToUpdate: UserCVEducation[] = [];

    if (!data.id) {
      dataToUpdate = [...(cv?.educations || []), data];
    } else {
      dataToUpdate = (cv?.educations || [])?.map((item) => {
        if (item.id === data.id) {
          return { ...item, ...data };
        }
        return item;
      });
    }

    try {
      await updateCV({
        id: cv?.id,
        type: 'educations',
        data: dataToUpdate,
      });
      toast.success('Education updated successfully');
      return true;
    } catch (error) {
      toast.error('Failed to update Education. Please try again later.');
      return false;
    }
  };

  const handleDragEnd = (
    event: any,
    dataType: 'skills' | 'experiences' | 'educations'
  ) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      let newDataToUpdate: any[] = [];
      if (dataType === 'skills') {
        setCVSkills((items) => {
          const oldIndex = items.findIndex((item) => item.id === active.id);
          const newIndex = items.findIndex((item) => item.id === over.id);

          newDataToUpdate = arrayMove(items, oldIndex, newIndex);
          return newDataToUpdate;
        });
      } else if (dataType === 'experiences') {
        setCVExperience((items) => {
          const oldIndex = items.findIndex((item) => item.id === active.id);
          const newIndex = items.findIndex((item) => item.id === over.id);

          newDataToUpdate = arrayMove(items, oldIndex, newIndex);
          return newDataToUpdate;
        });
      } else if (dataType === 'educations') {
        setCVEducation((items) => {
          const oldIndex = items.findIndex((item) => item.id === active.id);
          const newIndex = items.findIndex((item) => item.id === over.id);

          newDataToUpdate = arrayMove(items, oldIndex, newIndex);
          return newDataToUpdate;
        });
      }

      updateCV({
        id: cv?.id,
        type: dataType,
        data: newDataToUpdate,
      });
    }
  };

  useEffect(() => {
    setCVSkills(cv?.skills || []);
  }, [cv?.skills]);

  useEffect(() => {
    setCVEducation(cv?.educations || []);
  }, [cv?.educations]);

  useEffect(() => {
    setCVExperience(cv?.experiences || []);
  }, [cv?.experiences]);

  if (!user) return null;

  return (
    <>
      <div className="space-y-6">
        <div className="rounded-2xl p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
          <div className="space-y-6">
            <div className="flex items-center gap-4">
              <Image
                src={user.avatar || '/avatar/user-default.png'}
                alt={user.firstName || ''}
                width={100}
                height={100}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  e.currentTarget.src = '/avatar/user-default.png';
                }}
                className="h-[100px] w-[100px] rounded-full object-cover"
              />

              <div className="flex w-full items-center justify-between">
                <div className="text-bold line-clamp-1 flex-1">
                  {user.firstName} {user.lastName}
                </div>
              </div>
            </div>

            <hr className="text-[#F4F4F4]" />

            <div className="w-full space-y-4">
              <div className="flex flex-row items-center justify-between text-lg font-semibold text-[#0D1321]">
                <span>About</span>
                <ActionIcon
                  variant="outline"
                  size="sm"
                  onClick={() => setAboutModal(true)}
                >
                  <EditIcon className="h-5 w-5" />
                </ActionIcon>
              </div>
              {cv?.about ? (
                <p className="mt-4 whitespace-pre-line">{cv?.about}</p>
              ) : (
                <p className="text-sm italic">
                  Add an About section to highlight your strengths and career
                  objectives.
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4 rounded-2xl p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">Work experience</div>
            <ActionIcon
              variant="outline"
              size="sm"
              className="flex-shrink-0"
              onClick={() => setExperienceModal(true)}
            >
              <PlusIcon className="h-5 w-5" />
            </ActionIcon>
          </div>

          <div>
            {!cvExperience?.length ? (
              <div className="text-sm italic">
                Showcase your work experience to highlight your career journey.
              </div>
            ) : (
              <ul role="list" className="divide-y divide-gray-200">
                <DndContext
                  onDragEnd={(event) => {
                    handleDragEnd(event, 'experiences');
                  }}
                  sensors={sensors}
                  collisionDetection={closestCenter}
                >
                  <SortableContext
                    items={cvExperience.map((exp) => exp.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {cvExperience.map((exp) => (
                      <SortableItem key={exp.id} id={exp.id}>
                        <div className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]">
                          <div className="text-sm text-[#0D1321]">
                            {exp.startMonth &&
                              getMonthName(Number(exp.startMonth))}{' '}
                            {exp.startYear}{' '}
                            {exp.endYear
                              ? `- ${exp.endMonth && getMonthName(Number(exp.endMonth))}
                        ${exp.endYear}`
                              : '- Present'}
                          </div>

                          <div className="w-full">
                            <div className="flex w-full gap-2">
                              <div>
                                <div className="font-semibold">{exp.title}</div>
                                <p>{exp.company}</p>
                              </div>
                              <div className="ml-auto min-w-16 space-x-2">
                                <ActionIcon
                                  variant="outline"
                                  className="!h-6 !w-6 flex-shrink-0"
                                  onClick={() => {
                                    setSelectedData(exp);
                                    setExperienceModal(true);
                                  }}
                                >
                                  <EditIcon className="h-4 w-4" />
                                </ActionIcon>
                                <ActionIcon
                                  variant="outline"
                                  className="!h-6 !w-6 flex-shrink-0"
                                  onClick={() => handleDeleteExperience(exp.id)}
                                >
                                  <DeleteIcon className="h-4 w-4" />
                                </ActionIcon>
                              </div>
                            </div>

                            <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                              {exp.description}
                            </p>
                          </div>
                        </div>
                      </SortableItem>
                    ))}
                  </SortableContext>
                </DndContext>

                {/* {cv.experiences.map((exp, idx) => (
                  <li key={idx} className="py-2">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-[180px_minmax(0,1fr)]">
                      <div className="text-sm text-[#0D1321]">
                        {exp.startMonth && getMonthName(Number(exp.startMonth))}{' '}
                        {exp.startYear}{' '}
                        {exp.endYear
                          ? `- ${exp.endMonth && getMonthName(Number(exp.endMonth))}
                        ${exp.endYear}`
                          : '- Present'}
                      </div>

                      <div className="w-full">
                        <div className="flex w-full gap-2">
                          <div className="font-semibold">{exp.title}</div>
                          <div className="ml-auto min-w-16 space-x-2">
                            <ActionIcon
                              variant="outline"
                              className="!h-6 !w-6 flex-shrink-0"
                              onClick={() => {
                                setSelectedData(exp);
                                setExperienceModal(true);
                              }}
                            >
                              <EditIcon className="h-4 w-4" />
                            </ActionIcon>
                            <ActionIcon
                              variant="outline"
                              className="!h-6 !w-6 flex-shrink-0"
                              onClick={() => handleDeleteExperience(exp.id)}
                            >
                              <DeleteIcon className="h-4 w-4" />
                            </ActionIcon>
                          </div>
                        </div>

                        <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                          {exp.description}
                        </p>
                      </div>
                    </div>
                  </li>
                ))} */}
              </ul>
            )}
          </div>
        </div>

        <div className="space-y-4 rounded-2xl p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">Skills</div>
            <ActionIcon
              variant="outline"
              size="sm"
              className="flex-shrink-0"
              onClick={() => setSkillModal(true)}
            >
              <PlusIcon className="h-5 w-5" />
            </ActionIcon>
          </div>

          <div className="w-full">
            {!cvSkills?.length ? (
              <div className="text-sm italic">
                Add skills that reflect your strengths and expertise.
              </div>
            ) : (
              <ul role="list" className="divide-y divide-gray-200">
                <DndContext
                  onDragEnd={(event) => {
                    handleDragEnd(event, 'skills');
                  }}
                  sensors={sensors}
                  collisionDetection={closestCenter}
                >
                  <SortableContext
                    items={cvSkills.map((skill) => skill.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {cvSkills.map((skill) => (
                      <SortableItem key={skill.id} id={skill.id}>
                        <div className="flex w-full items-center justify-between gap-2 py-2">
                          <div>{skill.skill}</div>
                          <div className="space-x-2">
                            <ActionIcon
                              variant="outline"
                              className="!h-6 !w-6 flex-shrink-0"
                            >
                              <DeleteIcon
                                className="h-4 w-4"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  handleDeleteSkill(skill.id);
                                }}
                              />
                            </ActionIcon>
                          </div>
                        </div>
                      </SortableItem>
                    ))}
                  </SortableContext>
                </DndContext>
              </ul>
            )}
          </div>
        </div>

        <div className="space-y-4 rounded-2xl p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">Education</div>
            <ActionIcon
              variant="outline"
              size="sm"
              className="flex-shrink-0"
              onClick={() => setEducationModal(true)}
            >
              <PlusIcon className="h-5 w-5" />
            </ActionIcon>
          </div>

          <div>
            {!cvEducation?.length ? (
              <div className="text-sm italic">
                Share your educational background to strengthen your profile.
              </div>
            ) : (
              <ul role="list" className="divide-y divide-gray-200">
                <DndContext
                  onDragEnd={(event) => {
                    handleDragEnd(event, 'educations');
                  }}
                  sensors={sensors}
                  collisionDetection={closestCenter}
                >
                  <SortableContext
                    items={cvEducation.map((education) => education.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {cvEducation.map((education) => (
                      <SortableItem key={education.id} id={education.id}>
                        <div className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]">
                          <div className="text-sm text-[#0D1321]">
                            {education.startMonth &&
                              getMonthName(Number(education.startMonth))}{' '}
                            {education.startYear}{' '}
                            {education.endYear
                              ? `- ${education.endMonth ? getMonthName(Number(education.endMonth)) : ''}
                        ${education.endYear}`
                              : ''}
                          </div>

                          <div className="w-full">
                            <div className="flex w-full flex-row justify-between gap-2">
                              <div>
                                <p className="font-semibold">
                                  {education.school}
                                </p>
                                <p className="text-sm text-[#484848]">
                                  {[
                                    education.degree,
                                    education.field,
                                    education.grade,
                                  ]
                                    .filter(Boolean)
                                    .join(', ')}
                                </p>
                              </div>
                              <div className="min-w-16 space-x-2">
                                <ActionIcon
                                  variant="outline"
                                  className="!h-6 !w-6 flex-shrink-0"
                                  onClick={() => {
                                    setSelectedData(education);
                                    setEducationModal(true);
                                  }}
                                >
                                  <EditIcon className="h-4 w-4" />
                                </ActionIcon>
                                <ActionIcon
                                  variant="outline"
                                  className="!h-6 !w-6 flex-shrink-0"
                                  onClick={() =>
                                    handleDeleteEducation(education.id)
                                  }
                                >
                                  <DeleteIcon className="h-4 w-4" />
                                </ActionIcon>
                              </div>
                            </div>

                            <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                              {education.description}
                            </p>
                          </div>
                        </div>
                      </SortableItem>
                    ))}
                  </SortableContext>
                </DndContext>
              </ul>
            )}
          </div>
        </div>
      </div>

      {experienceModal && (
        <ExperienceModal
          open={experienceModal}
          onClose={() => {
            setExperienceModal(false);
            setSelectedData(undefined);
          }}
          isUpdating={isUpdating}
          initialData={selectedData}
          onSave={handleUpdateExperience}
        />
      )}

      {skillModal && (
        <SkillModal
          open={skillModal}
          onClose={() => setSkillModal(false)}
          isUpdating={isUpdating}
          onSave={handleAddSkills}
        />
      )}

      {educationModal && (
        <EducationModal
          open={educationModal}
          onClose={() => {
            setEducationModal(false);
            setSelectedData(undefined);
          }}
          isUpdating={isUpdating}
          initialData={selectedData}
          onSave={handleUpdateEducation}
        />
      )}

      {aboutModal && (
        <AboutModal
          open={aboutModal}
          initValue={cv?.about || ''}
          isUpdating={isUpdating}
          onClose={() => setAboutModal(false)}
          onSave={handleUpdateAbout}
        />
      )}
    </>
  );
}

const SortableItem = (props: any) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: props.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="flex flex-row items-center gap-2"
    >
      <GripVertical size={16} {...listeners} />
      {props.children}
    </li>
  );
};
