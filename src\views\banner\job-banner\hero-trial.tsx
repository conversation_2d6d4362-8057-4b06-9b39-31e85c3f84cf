'use client';

import UploadIcon from '@/views/icons/upload';
import React, { useState } from 'react';
import { Button } from 'rizzui';
import UploadCvModal from './upload-cv-modal';
import Image from 'next/image';

type HeroTrialProps = {
  primaryColor?: string; // ví dụ: '#0D1321'
  trialsThisMonth?: string; // '9,200 trials this month'
  interviewRate?: string; // '61% received interviews'
  medianDays?: string; // 'median 8 days to first interview'
};

export default function HeroTrial() {
  const [openUploadCv, setOpenUploadCv] = useState(false);

  return (
    <>
      <div className="grid grid-cols-1 items-center gap-10 lg:grid-cols-2 lg:gap-14">
        <div>
          <div className="font-semibold tracking-tight">
            <span className="block text-3xl leading-[1.05] md:text-4xl">
              You find real jobs and stand out with quick proof.
            </span>
          </div>

          <p className="mt-4 max-w-2xl leading-8 text-gray-700">
            You browse real listings and you add an optional mini-task to show
            your skill. You reach hiring managers faster with clear, simple
            proof.
          </p>

          {/* CTAs */}
          <div className="mt-4 flex flex-col gap-4 sm:flex-row">
            <Button
              className="bg-primary text-white"
              size="lg"
              onClick={() => setOpenUploadCv(true)}
            >
              <UploadIcon />
              <span className="ml-2">Upload CV to match roles</span>
            </Button>
          </div>

          {/* Stats */}
          <div
            // className="mt-6 flex flex-col gap-5 text-sm text-gray-700 sm:flex-row sm:items-center"
            className="mt-6 grid grid-cols-1 gap-3 text-sm text-gray-700 sm:grid-cols-3"
          >
            <div className="inline-flex items-start gap-3">
              <span className="inline-flex !h-6 !w-6 flex-shrink-0 items-center justify-center rounded-full ring-1 ring-slate-300">
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M2 12h3l2 7 4-14 3 7h8" />
                </svg>
              </span>
              <span className="text-wrap">
                9,200 roles
                <br />
                this month
              </span>
            </div>

            <div className="inline-flex items-start gap-3">
              <span className="inline-flex !h-6 !w-6 flex-shrink-0 items-center justify-center rounded-full ring-1 ring-slate-300">
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M16 14a4 4 0 1 0-8 0" />
                  <circle cx="12" cy="7" r="3" />
                  <path d="M22 21a6 6 0 0 0-12 0" />
                </svg>
              </span>
              <span className="text-wrap">
                61% reply rate when candidates attach proof
              </span>
            </div>

            <div className="inline-flex items-start gap-3">
              <span className="inline-flex !h-6 !w-6 flex-shrink-0 items-center justify-center rounded-full ring-1 ring-slate-300">
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <circle cx="12" cy="12" r="9" />
                  <path d="M12 7v5l3 3" />
                </svg>
              </span>
              <span className="text-wrap">
                Median 8 days to
                <br />
                first interview
              </span>
            </div>
          </div>
        </div>

        {/* RIGHT */}
        <div className="w-full">
          <Image
            src="/job/overview.png"
            alt="overview"
            width={588}
            height={373}
            className="h-full w-full object-cover"
            loader={({ src }) => src}
          />
          {/* <div className="relative w-full rounded-3xl border-2 border-dashed border-[#D6DCE5] bg-white">
            <div className="flex aspect-video w-full items-center justify-center rounded-3xl">
              <div className="px-6 text-center">
                <div className="mx-auto mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full ring-2 ring-slate-300">
                  <svg
                    viewBox="0 0 24 24"
                    className="h-6 w-6"
                    aria-hidden="true"
                    fill="currentColor"
                    style={{ color: '#9AA3B2' }}
                  >
                    <path d="M8 5.14v13.72a1 1 0 0 0 1.52.85l10.1-6.86a1 1 0 0 0 0-1.7L9.52 4.29A1 1 0 0 0 8 5.14Z" />
                  </svg>
                </div>

                <p className="text-base text-slate-500 sm:text-lg">
                  <span className="hidden sm:inline">
                    15s demo placeholder —{' '}
                  </span>
                  simulation flow (Search → Mini task → Score → Invite)
                </p>
              </div>
            </div>

            <div className="pointer-events-none absolute inset-0 rounded-3xl ring-1 ring-inset ring-slate-200/60"></div>
          </div> */}
        </div>
      </div>

      {openUploadCv && (
        <UploadCvModal
          open={openUploadCv}
          onClose={() => setOpenUploadCv(false)}
        />
      )}
    </>
  );
}
