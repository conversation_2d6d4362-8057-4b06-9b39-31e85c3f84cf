'use client';

import { JobStatus } from '@/api-requests/job';
import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { UserApplication } from '@/api-requests/user-profile';
import StartSimulationButton from '@/components/StartSimulationButton';
import useApplicationStatus from '@/hooks/use-application-status';
import useSimulationStatus from '@/hooks/use-simulation-status';
import { Image } from '@/shared/image';
import {
  convertApplicationStatus,
  convertSimulationStatus,
} from '@/utils/application-simulation-status';
import { safeFormatDate } from '@/utils/date';
import { getApplyModeText } from '@/views/candidate-list/candidates-table';
import { PlayCircle } from 'lucide-react';
import Link from 'next/link';
import { Badge, Button, Tooltip } from 'rizzui';

const getLocation = (application: UserApplication) => {
  if (application.job?.city || application.job?.country) {
    return `${application.job.city ? `${application.job.city}, ` : ''}${application.job.country}`;
  }
  if (application.org?.city || application.org?.country) {
    return `${application.org.city ? `${application.org.city}, ` : ''}${application.org.country}`;
  }
  return '';
};

interface IProps {
  application: UserApplication;
  onOpenDetail: (id: string) => void;
  onClickWithdraw: (id: string) => void;
}

export default function ApplicationRow({
  application,
  onOpenDetail,
  onClickWithdraw,
}: IProps) {
  const { getApplicationStatusClassName } = useApplicationStatus();
  const { getSimulationStatusClassName } = useSimulationStatus();

  const hasNextStep =
    application.simulationStatus && application.simulationStatus === 'active';
  const shouldContinueSimulation =
    (application.applicationStatus === ApplicationStatus.DRAFT &&
      application.job?.status &&
      application.job?.status !== JobStatus.CLOSED) ||
    (application.simulation?.status &&
      application.simulation?.status === 'published');

  const canWithdraw = [
    ApplicationStatus.SUBMITTED,
    ApplicationStatus.UNDER_REVIEW,
    ApplicationStatus,
  ].includes(application.applicationStatus);

  return (
    <div className="rounded-2xl border border-[#c3c3c3] p-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        {/* Left section */}
        <div className="flex min-w-0 items-start gap-4 md:max-w-[50%]">
          {application?.org?.logo || application?.job?.companyLogoUrl ? (
            <Image
              src={
                application?.org?.logo || application?.job?.companyLogoUrl || ''
              }
              fallbackSrc="/org/default-logo.png"
              alt={application.org?.name || application.job?.companyName}
              width={60}
              height={60}
              className="h-15 w-15 object-contain"
              loader={({ src }) => src}
            />
          ) : (
            <div
              className="!h-15 !w-15 rounded-full bg-gray-100"
              style={{ width: '60px', height: '60px' }}
            />
          )}
          <div className="min-w-0">
            <div className="flex flex-wrap items-center gap-2">
              <p
                className="text-left text-base font-medium hover:underline"
                onClick={() => onOpenDetail(application._id)}
              >
                {application.job?.title || application.simulation.name}
              </p>
              {/* <Badge variant="outline" size="sm">
                Closed
              </Badge> */}
              {(application.job && application.job?.status === 'closed') ||
                (application.simulation &&
                  application.simulation?.status !== 'published' && (
                    <Badge variant="outline" size="sm">
                      Closed
                    </Badge>
                  ))}
            </div>
            <div className="text-sm text-gray-600">
              <p className="truncate">
                {application.org?._id ? (
                  <Link
                    href={`/org/${application.org?._id}`}
                    target="_blank"
                    className="hover:underline"
                  >
                    <span>
                      {application.org?.name || application.job?.companyName}
                    </span>
                  </Link>
                ) : (
                  <span>
                    {application.org?.name || application.job?.companyName}
                  </span>
                )}
              </p>
              <p className="truncate">{getLocation(application)}</p>
            </div>
            <div className="mt-2 flex flex-wrap gap-1.5">
              <Badge variant="outline" size="sm">
                {getApplyModeText(application.applyMode)}
              </Badge>
              {application.applyMode === 'cv' &&
                !!application.quickQuestions?.length && (
                  <Badge variant="outline" size="sm">
                    Questions
                  </Badge>
                )}
              {application.applyMode === 'simulation' &&
                !!application.simulationStatus && (
                  <Badge
                    variant="flat"
                    size="sm"
                    className={getSimulationStatusClassName(
                      application.simulationStatus as 'active' | 'completed'
                    )}
                  >
                    {convertSimulationStatus(application.simulationStatus)}
                  </Badge>
                )}
            </div>
          </div>
        </div>

        {/* Right meta */}
        <div className="grid grid-cols-3 items-center gap-6 md:text-right">
          <div className="text-sm">
            <div className="font-bold">Applied</div>
            <div>
              <Tooltip
                content={safeFormatDate(application.appliedAt, {
                  format: 'full',
                })}
                size="sm"
                color="invert"
              >
                <span className="text-sm">
                  {safeFormatDate(application.appliedAt, {
                    format: 'long',
                  })}
                </span>
              </Tooltip>
              {/* {safeFormatDate(application.appliedAt, { format: 'short' })} */}
            </div>
          </div>

          <div className="text-center md:text-right">
            <div className="text-sm font-bold">Match</div>
            {application.matchPercentage !== undefined ? (
              <span className="font-bold">{application.matchPercentage}%</span>
            ) : (
              <Tooltip
                color="invert"
                content="You've not completed the simulation"
              >
                <span>--%</span>
              </Tooltip>
            )}
          </div>

          <div className="flex items-center justify-end">
            <Badge
              variant="flat"
              size="sm"
              className={getApplicationStatusClassName(
                application.applicationStatus
              )}
            >
              {convertApplicationStatus(application.applicationStatus)}
            </Badge>
          </div>
        </div>
      </div>

      <hr className="my-4 border border-t-[#c3c3c3]" />

      {/* Actions */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="text-sm text-gray-500">
          {application.job?.status === JobStatus.CLOSED ? (
            <span>This job is closed to new actions.</span>
          ) : hasNextStep ? (
            <span>Continue to complete the application.</span>
          ) : (
            <>No pending actions.</>
          )}
        </div>

        <div className="flex items-center gap-2">
          {shouldContinueSimulation && (
            <StartSimulationButton
              jobId={application.jobId || ''}
              simId={application.simulationId || ''}
              buttonProps={{ className: 'text-white gap-1' }}
            >
              <PlayCircle className="h-4 w-4" /> Continue
            </StartSimulationButton>
          )}
          {/* {application.applicationStatus !== ApplicationStatus.DRAFT && (
            <Button
              variant="outline"
              className="gap-1"
              onClick={() => onOpenDetail(application._id)}
            >
              <Eye className="h-4 w-4" /> View details
            </Button>
          )} */}
          {canWithdraw && (
            <Button
              variant="outline"
              className="gap-1 border-red-500 text-red-500 hover:border-red-700 hover:text-red-700"
              onClick={() => {
                onClickWithdraw(application._id);
              }}
            >
              Withdraw
            </Button>
          )}
        </div>
      </div>

      {/* Interview quick indicator (optional) */}
      {/* {application.status === 'Interview' && (
        <div className=" mt-3 text-sm">
          {interviewState === 'invite_pending' && (
            <span>
              Interview invite pending — respond by{' '}
              {formatDate(application.interview!.respondBy)}
            </span>
          )}
          {interviewState === 'scheduled' && (
            <span>
              Interview scheduled for{' '}
              {formatDate(application.interview!.startAt!)}{' '}
            </span>
          )}
          {interviewState === 'expired' && (
            <span>Interview invite expired</span>
          )}
        </div>
      )} */}
    </div>
  );
}
