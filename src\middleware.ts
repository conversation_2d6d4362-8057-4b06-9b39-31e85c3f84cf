import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { AT_COOKIE } from './app/api/auth/_utils';
import { decodeToken } from './utils/token';
import { refreshToken } from './lib/auth/refresh-token';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const countryName = request.headers.get('cloudfront-viewer-country-name');
  const at = request.cookies.get(AT_COOKIE)?.value;
  const tokenDecoded = decodeToken(at || '');

  const response = NextResponse.next();
  await refreshToken(request, response);

  if (countryName) {
    response.cookies.set('country-name', countryName, {
      path: '/',
      httpOnly: false,
      secure: true,
      sameSite: 'lax',
    });
  }

  const orgIdPattern = /^\/org\/[^\/]+$/;
  const isOrgIdRoute = orgIdPattern.test(pathname);

  // Employer/Partner mangement routes
  if (pathname.startsWith('/org/admin')) {
    if (
      !tokenDecoded ||
      !tokenDecoded?.role ||
      !['employer', 'education'].includes(tokenDecoded?.role)
    ) {
      const url = new URL('/', request.url);
      return NextResponse.redirect(url);
    }

    return response;
  }

  // Admin routes
  if (pathname.startsWith('/admin')) {
    // pathname !== '/admin/login'
    if (tokenDecoded?.role && tokenDecoded.role !== 'admin') {
      const url = new URL('/', request.url);
      return NextResponse.redirect(url);
    }

    if (pathname === '/admin/login' && tokenDecoded?.role === 'admin') {
      const url = new URL('/admin/candidates', request.url);
      return NextResponse.redirect(url);
    }
  } else {
    if (tokenDecoded?.role === 'admin') {
      const url = new URL('/admin/candidates', request.url);
      return NextResponse.redirect(url);
    }
  }

  return response;
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|images|favicon.ico|firebase-messaging-sw.js$).*)',
  ],
};
