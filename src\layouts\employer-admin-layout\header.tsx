'use client';

import { Role } from '@/api-requests/user/types';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { resetFiltersAtom, resetSearchAtom } from '@/store/job-atom';
import { searchModeAtom } from '@/store/talent-atom';
import { userAtom } from '@/store/user-atom';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import MessageIcon from '@/views/icons/message';
import NotificationIcon from '@/views/icons/notification';
import { useAtom } from 'jotai';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { Button } from 'rizzui';
import logoImage from '../../../public/ic-io-logo-light.png';
import ProfileDropdown from './profile-dropdown';

interface IProps {
  onOpenMenu?: () => void;
}

export default function Header({ onOpenMenu }: IProps) {
  const pathname = usePathname();

  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, resetSearch] = useAtom(resetSearchAtom);
  const [, setSearchMode] = useAtom(searchModeAtom);
  const [user] = useAtom(userAtom);

  const { logout } = useAuthActions();

  const [openLoginModal, setOpenLoginModal] = useState(false);

  const handleLogoClick = () => {
    resetFilters();
    resetSearch();
  };

  const handleClickLink = () => {
    setSearchMode(false);
  };

  return (
    <>
      <header className="sticky top-0 z-[100] flex h-[80px] items-center bg-white px-4 shadow-md xl:px-0">
        <div className="mx-auto flex w-full items-center justify-between px-4 lg:px-6">
          {/* Logo */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-4">
              {!!onOpenMenu && (
                <Menu
                  onClick={onOpenMenu}
                  className="cursor-pointer xl:hidden"
                />
              )}
              <Link onClick={handleLogoClick} href="/">
                <div className="flex items-center space-x-2">
                  <Image
                    src={logoImage}
                    alt="Industry Connect Logo"
                    width={0}
                    height={32}
                    className="h-6 w-auto lg:h-8"
                    loader={({ src }) => src}
                  />
                </div>
              </Link>
              <Link onClick={handleLogoClick} href="/employer/landing">
                <div className="hover:cursor-pointer">Employer</div>
              </Link>
            </div>
          </div>

          {/* Sign In Button */}
          {user && user.role === Role.EMPLOYER ? (
            <div className="flex items-center gap-4">
              {/* Message and Notification */}
              <div className="flex items-center gap-4">
                <div className="relative">
                  <MessageIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>

                <div className="relative">
                  <NotificationIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>
              </div>

              {/* Separator */}
              <div className="h-6 w-px bg-gray-300" />

              {/* Avatar */}
              <div className="flex h-full items-center space-x-4">
                <ProfileDropdown userInfo={user} />
              </div>
            </div>
          ) : (
            <Button
              className="bg-primary text-white"
              onClick={() => setOpenLoginModal(true)}
            >
              Sign In
            </Button>
          )}
        </div>
      </header>

      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.EMPLOYER}
        />
      )}
    </>
  );
}
