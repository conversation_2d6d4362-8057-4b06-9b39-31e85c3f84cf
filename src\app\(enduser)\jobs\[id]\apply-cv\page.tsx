import { getJob } from '@/api-requests/job/get-by-id';
import ApplyCVFullscreen from '@/views/job/job-detail/apply-cv-fullscreen';

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function ApplyWithCVPage({ params }: PageProps) {
  const { id } = await params;
  const job = await getJob(id);

  if (!job) return null;

  return (
    <div className="min-h-[calc(100vh-80px)] bg-[#F8F8F8]">
      <div className="mx-auto max-w-7xl">
        <ApplyCVFullscreen
          job={{
            jobId: job.jobId,
            title: job.title,
            companyLogo: job.companyLogoUrl,
            companyName: job.companyName,
            orgId: job.orgId,
          }}
          simulationId={job.simulation?.id || ''}
        />
      </div>
    </div>
  );
}
