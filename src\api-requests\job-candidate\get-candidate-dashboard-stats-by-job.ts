import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import { JobCandidate, JobCandidateQueryKeys } from './types';

export async function getCandidateDashBoardStatsByJob(jobId: string) {
  const reps = await axiosInstance.get(
    API_ENDPONTS.GET_CANDIDATE_DASHBOARD_STATS_BY_JOB.replace(':jobId', jobId || '')
  );

  return reps.data;
}

export function useGetCandidateDashboardStatsByJob(jobId: string) {
  return useQuery({
    queryKey: [JobCandidateQueryKeys.GET_CANDIDATE_DASHBOARD_STATS_BY_JOB, { jobId }],
    queryFn: () => getCandidateDashBoardStatsByJob(jobId),
    enabled: !!jobId,
  });
}
