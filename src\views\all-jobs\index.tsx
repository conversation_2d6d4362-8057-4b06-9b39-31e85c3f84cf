'use client';

import { useAtom } from 'jotai';
import AllJobsFilter from './filter';
import AllJobsTable from './table';
import { userAtom } from '@/store/user-atom';
import { useListOrgJob } from '@/api-requests/job/get-org-jobs';
import { useRef, useState } from 'react';
import { Job } from '@/api-requests/job';
import { debounce } from 'lodash';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import { orgAtom } from '@/store/organization-atom';

export default function AllJobs() {
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState<string>('');
  const [location, setLocation] = useState<string>('');
  const [status, setStatus] = useState<SelectOption | null>(null);
  const [jobType, setJobType] = useState<SelectOption | null>(null);
  const [simulation, setSimulation] = useState<SelectOption | null>(null);
  const [sortBy, setSortBy] = useState<SelectOption | null>(null);
  const [applicants, setApplicants] = useState<number>();
  const [created, setCreated] = useState<number>();
  const [lastAppliedAt, setLastAppliedAt] = useState<number>();

  const searchRef = useRef<HTMLInputElement>(null!);
  const searchLocationRef = useRef<HTMLInputElement>(null!);

  const { data, isLoading, refetch } = useListOrgJob({
    // userId: user?.id as string,
    orgId: org?._id as string,
    page,
    limit: LIMIT,
    title: search,
    status: status?.value,
    location,
    jobType: jobType?.value,
    simLevel: simulation?.value,
    applicants,
    created,
    lastAppliedAt,
  });

  const handleSort = (option: SelectOption | null) => {
    setSortBy(option);
    if (option) {
      if (option.label.includes('Candidates')) {
        setApplicants(option.label.includes('ascending') ? -1 : 1);
        setCreated(0);
        setLastAppliedAt(0);
      } else if (option.label.includes('Created')) {
        setCreated(option.label.includes('newest') ? -1 : 1);
        setApplicants(0);
        setLastAppliedAt(0);
      } else if (option.label.includes('Last apply')) {
        setLastAppliedAt(option.label.includes('recent') ? -1 : 1);
        setApplicants(0);
        setCreated(0);
      }
    } else {
      setApplicants(0);
      setCreated(0);
      setLastAppliedAt(0);
    }
    setPage(1);
  };

  const handleFilter = (option: SelectOption | null, type: string) => {
    if (type === 'jobType') {
      setJobType(option);
    } else if (type === 'simulation') {
      setSimulation(option);
    } else if (type === 'status') {
      setStatus(option);
    }
    setPage(1);
  };

  const handleSearch = debounce((value: string, type: string) => {
    if (!value || value.trim().length > 2) {
      if (type === 'title') {
        setSearch(value);
      } else if (type === 'location') {
        setLocation(value);
      }
      setPage(1);
    }
  }, 500);

  const handleReset = () => {
    setSearch('');
    setLocation('');
    setStatus(null);
    setJobType(null);
    setSimulation(null);
    setSortBy(null);
    if (searchRef.current) {
      searchRef.current.value = '';
    }
    if (searchLocationRef.current) {
      searchLocationRef.current.value = '';
    }
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <AllJobsFilter
        searchProps={{
          onChange: (e) => handleSearch(e.target.value, 'title'),
          ref: searchRef,
        }}
        searchLocationProps={{
          onChange: (e) => handleSearch(e.target.value, 'location'),
          ref: searchLocationRef,
        }}
        statusProps={{
          value: status,
          onClear: () => setStatus(null),
          onChange: (val) => handleFilter(val, 'status'),
        }}
        jobTypeProps={{
          value: jobType,
          onClear: () => setJobType(null),
          onChange: (val) => handleFilter(val, 'jobType'),
        }}
        simulationProps={{
          value: simulation,
          onClear: () => setSimulation(null),
          onChange: (val) => handleFilter(val, 'simulation'),
        }}
        sortByProps={{
          value: sortBy,
          onClear: () => setSortBy(null),
          onChange: handleSort,
        }}
        resetProps={{
          onClick: handleReset,
          disabled: isLoading,
        }}
      />

      <AllJobsTable
        jobData={data as ApiListResponse<Job>}
        isLoading={isLoading}
        page={page}
        setPage={setPage}
        refetch={refetch}
      />
    </div>
  );
}
