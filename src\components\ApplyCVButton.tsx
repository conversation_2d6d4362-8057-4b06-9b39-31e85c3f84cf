'use client';

import { Role } from '@/api-requests/user/types';
import { userAtom } from '@/store/user-atom';
import { CAREER_AT_TOKEN, getClientSideCookie } from '@/utils/http-client';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import { useAtom } from 'jotai';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { createPortal } from 'react-dom';
import toast from 'react-hot-toast';
import { Button } from 'rizzui';

interface IProps {
  jobId: string;
  simId: string;
  buttonProps?: React.ComponentProps<typeof Button>;
  children?: React.ReactNode;
  onSuccess?: () => void;
  onErrorJobApplied?: () => void;
}

export default function ApplyCVButton({
  jobId,
  simId,
  buttonProps,
  children,
  onSuccess,
  onErrorJobApplied,
}: IProps) {
  const router = useRouter();
  const [user] = useAtom(userAtom);
  const [openLoginModal, setOpenLoginModal] = useState(false);
  // const [openApplyCVModal, setOpenApplyCVModal] = useState(false);

  const checkUserLogin = () => {
    const accessToken = getClientSideCookie(CAREER_AT_TOKEN);
    if (!accessToken || !user) {
      setOpenLoginModal(true);
      return false;
    }
    if (user?.role !== Role.USER) {
      toast.error('Only users can apply for this job.');
      return false;
    }

    return true;
  };

  const handleApplyCV = () => {
    if (!checkUserLogin()) return;
    // setOpenApplyCVModal(true);
    router.push(`/jobs/${jobId}/apply-cv`);
  };

  const handleLoginSuccess = () => {
    router.push(`/jobs/${jobId}/apply-cv`);
    // setOpenApplyCVModal(true);
  };

  return (
    <>
      <Button onClick={handleApplyCV} {...buttonProps}>
        {children || 'Apply with CV'}
      </Button>
      {openLoginModal &&
        createPortal(
          <SignInModal
            open={openLoginModal}
            onClose={() => setOpenLoginModal(false)}
            role={Role.USER}
            onLoginSuccess={handleLoginSuccess}
          />,
          document.body
        )}
    </>
  );
}
