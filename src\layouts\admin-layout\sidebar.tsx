'use client';

import { useAuthActions } from '@/hooks/use-auth-actions';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import ManagerIcon from '@/views/icons/manager';
import { useAtom } from 'jotai';
import {
  ChevronDownIcon
} from 'lucide-react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { Accordion, Avatar } from 'rizzui';
import logoImage from '../../../public/ic-io-logo-light.png';
import ProfileDropdown from '../employer-admin-layout/profile-dropdown';

const getOrgSidebarMenu = () => {
  return [
    {
      icon: ManagerIcon,
      label: 'Manage',
      key: 'Manage',
      activePaths: ['/admin/simulations', '/admin/candidates', '/admin/jobs'],
      children: [
        // {
        //   label: 'Jobs',
        //   key: 'Jobs',
        //   link: '/admin/jobs',
        // },
        {
          label: 'Candidates',
          key: 'Candidates',
          link: '/admin/candidates',
        },
        // {
        //   label: 'Talent Search',
        //   key: 'Talent Search',
        //   link: '/admin/talents',
        // },
        // { label: 'Members', key: 'Members', link: '/admin/members' },
        // {
        //   label: 'Subscribe',
        //   key: 'Subscribe',
        //   link: '/admin/subscribe',
        // },
      ],
    },
  ];
};

export default function Sidebar() {
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const router = useRouter();
  const pathname = usePathname();

  const { logout } = useAuthActions();

  const menu = getOrgSidebarMenu();

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-16 flex-row items-center justify-center border-b text-lg">
        <Image
          src={logoImage}
          alt="Logo"
          width={0}
          height={32}
          className="h-6 w-auto lg:h-8"
          loader={({ src }) => src}
        />
        <span className="ml-2 rounded bg-[#0D1321] px-2 py-1 text-xs text-white">
          Admin
        </span>
      </div>

      <nav className="flex-1 space-y-2 overflow-y-auto px-2 py-4">
        {menu.map((item) =>
          item.children ? (
            <Accordion
              key={item.key}
              defaultOpen={
                item.activePaths.find((path) => pathname.includes(path)) !==
                  undefined ||
                (item.children || []).find((child) =>
                  pathname.includes(child.link)
                ) !== undefined
              }
            >
              <Accordion.Header>
                {({ open }) => (
                  <div
                    className={cn(
                      'flex w-full cursor-pointer flex-row items-center rounded-md px-3 py-2 font-medium',
                      item.children.find((child) =>
                        pathname.includes(child.link)
                      ) !== undefined && 'bg-[#0D1321] text-white'
                    )}
                  >
                    {item.icon && <item.icon className="mr-4 h-5 w-5" />}
                    {item.label}
                    <ChevronDownIcon
                      className={cn(
                        'ml-auto h-4 w-4 -rotate-90 transform transition-transform duration-300',
                        open && 'rotate-0'
                      )}
                    />
                  </div>
                )}
              </Accordion.Header>
              <Accordion.Body>
                <div className="ml-4 mt-1 flex flex-col space-y-1">
                  {item.children.map((child) => (
                    <button
                      key={child.key}
                      onClick={() => {
                        if (child.link) {
                          router.push(child.link);
                        }
                      }}
                      className={cn(
                        'flex h-8 cursor-pointer flex-row items-center gap-3 rounded-lg px-4 text-left text-sm',
                        pathname.includes(child.link)
                          ? 'bg-[#EBEBEB]' //'border-b-2 border-primary'
                          : 'hover:bg-[#EBEBEB]'
                      )}
                    >
                      <span className="h-2 w-2 rounded-full bg-primary"></span>
                      {child.label}
                    </button>
                  ))}
                </div>
              </Accordion.Body>
            </Accordion>
          ) : (
            <button
              key={item.key}
              onClick={() => {
                if ((item as any).link) {
                  router.push((item as any).link);
                }
              }}
              className={cn(
                'flex w-full flex-row items-center rounded-md px-3 py-2 text-left font-medium',
                item.activePaths.find((path) => pathname.includes(path)) !==
                  undefined
                  ? 'bg-primary text-white'
                  : 'hover:bg-[#EBEBEB]'
              )}
            >
              {item.icon && <item.icon className="mr-4 h-5 w-5" />}
              {item.label}
            </button>
          )
        )}
      </nav>

      {/* Avatar */}
      {!!user && (
        <div className="mt-auto">
          <ProfileDropdown
            userInfo={user}
            dropdownTrigger={
              <div className="flex flex-row items-center gap-1">
                <Avatar
                  name={`${user.firstName} ${user.lastName}`}
                  src={user.avatar || '/avatar/user-default.png'}
                  size="md"
                  className="h-10 w-10 cursor-pointer rounded-full !bg-transparent object-cover"
                />
                <div className="text-left">
                  <p>
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-xs">{user.email}</p>
                </div>
              </div>
            }
          />
        </div>
      )}
    </div>
  );
}
