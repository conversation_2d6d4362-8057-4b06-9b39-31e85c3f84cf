'use client';

import Image from 'next/image';
import { useState } from 'react';
import { Button } from 'rizzui';
import UploadCvModal from '../banner/job-banner/upload-cv-modal';

export default function SmarterWay() {
  const [openUploadCv, setOpenUploadCv] = useState(false);
  return (
    <>
      <div className="z-10 mx-auto w-full max-w-[1440px] px-4 xl:px-0">
        <div
          className="relative rounded-2xl bg-primary bg-cover bg-center bg-no-repeat px-6 py-4 md:px-16 md:py-10"
          style={{
            backgroundImage: 'url("/job/smarter-way-banner.png")',
          }}
        >
          <div className="relative z-10 text-center md:max-w-lg md:text-left">
            <h2 className="text-2xl font-semibold text-white md:text-3xl">
              A Smarter Way to Get Hired
            </h2>
            <p className="md:text-md mt-4 text-base text-white">
              Stop scrolling and add a quick proof that makes hiring easy.
            </p>
            {/* <Link href="/find-jobs"> */}
            <Button
              variant="flat"
              className="mt-6 rounded-lg bg-white text-black"
              size="lg"
              onClick={() => setOpenUploadCv(true)}
            >
              {/* Start Your Smart Job Search */}Start with my CV
            </Button>
            {/* </Link> */}
          </div>

          <div className="absolute bottom-0 right-0 z-0 h-[85%]">
            <Image
              src="/job/smarter-way.png"
              alt="Smarter Way"
              width={0}
              height={0}
              className="h-full w-auto object-contain"
              loader={({ src }) => src}
            />
          </div>
        </div>
      </div>
      {openUploadCv && (
        <UploadCvModal
          open={openUploadCv}
          onClose={() => setOpenUploadCv(false)}
        />
      )}
    </>
  );
}
