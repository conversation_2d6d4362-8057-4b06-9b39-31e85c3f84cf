import { <PERSON><PERSON>, <PERSON><PERSON>, Button } from 'rizzui';
import {
  ProfileInfo,
  formatSalary,
  formatWorkType,
} from '../profile/user/profile-detail';
import { JobCandidate } from '@/api-requests/job-candidate/types';
import { Paperclip, Eye, Download } from 'lucide-react';
import Link from 'next/link';

const getMonthName = (humanMonth: number) => {
  return new Date(new Date().getFullYear(), humanMonth - 1).toLocaleString(
    'en-US',
    {
      month: 'short',
    }
  );
};

const formatWorkPlace = (workPlaces: string[] | undefined) => {
  if (!workPlaces || workPlaces.length === 0) return '-';
  return workPlaces
    .map((place) => {
      switch (place) {
        case 'remote':
          return 'Remote';
        case 'hybrid':
          return 'Hybrid';
        case 'onsite':
          return 'Onsite';
        default:
          return place;
      }
    })
    .join(', ');
};

interface IProps {
  candidate: JobCandidate;
}

export default function ProfileTab({ candidate }: IProps) {
  const cvData = candidate?.cvData;
  const userProfile = candidate?.userProfileSnapshot;

  // Use skills from cvData first, fallback to userProfile
  const skills = cvData?.skills?.length ? cvData.skills : userProfile?.skills;

  if (!cvData && !userProfile)
    return <div className="p-4">No profile found</div>;

  return (
    <div className="space-y-4">
      {/* About and Candidate Facts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* About Section */}
        <div className="lg:col-span-2">
          <div className="h-full space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
            <div>
              <div className="mb-2 text-lg font-bold">About</div>
            </div>
            <div className="whitespace-pre-line text-sm text-gray-800">
              {cvData?.about}
            </div>
          </div>
        </div>

        {/* Candidate Facts */}
        <div className="lg:col-span-1">
          <div className="h-full space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
            <div className="text-lg font-bold">Candidate Facts</div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Expected salary</span>
                <span className="text-sm font-medium">
                  {formatSalary(userProfile?.expectedSalary)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Notice period</span>
                <span className="text-sm font-medium">
                  {userProfile?.expectedSalary?.period || '-'}
                </span>
              </div>

              {userProfile?.workTypes && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Work type</span>
                  <span className="text-sm font-medium">
                    {formatWorkType(userProfile.workTypes)}
                  </span>
                </div>
              )}
              {userProfile?.workPlaces && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Work place</span>
                  <span className="text-sm font-medium">
                    {formatWorkPlace(userProfile.workPlaces)}
                  </span>
                </div>
              )}
              {userProfile?.industries && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Industries</span>
                  <span className="text-sm font-medium">
                    {userProfile.industries.join(', ')}
                  </span>
                </div>
              )}
              {userProfile?.levels && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Levels</span>
                  <span className="text-sm font-medium">
                    {userProfile.levels.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Work Experience */}
      <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
        <div className="text-lg font-bold">Work experience</div>
        {!!cvData?.experiences?.length ? (
          <ul role="list" className="divide-y divide-gray-200">
            {cvData.experiences.map((exp, idx) => (
              <li key={exp.id || idx} className="py-4">
                <div className="flex flex-col gap-2 sm:flex-row sm:gap-4">
                  <div className="text-sm text-gray-500 sm:w-48 sm:flex-shrink-0">
                    {exp.startMonth && getMonthName(Number(exp.startMonth))}{' '}
                    {exp.startYear}{' '}
                    {exp.endYear
                      ? `- ${exp.endMonth && getMonthName(Number(exp.endMonth))} ${exp.endYear}`
                      : '- Present'}
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-900">
                      {exp.title} • {exp.company}
                    </div>
                    {exp.description && (
                      <p className="mt-1 whitespace-pre-line text-sm text-gray-600">
                        {exp.description}
                      </p>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500">No work experience available</p>
        )}
      </div>

      {/* Skills and Education */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Skills */}
        <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
          <div className="text-lg font-bold">Skills</div>
          {skills?.length ? (
            <div className="flex flex-wrap gap-2">
              {(Array.isArray(skills) && typeof skills[0] === 'string'
                ? skills
                : Array.isArray(skills)
                  ? skills.map((s: any) => s.skill)
                  : []
              ).map((skill: string, idx: number) => (
                <Badge key={idx} variant="outline" size="sm">
                  {skill}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No skills available</p>
          )}
        </div>

        {/* Education */}
        <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
          <div className="text-lg font-bold">Education</div>
          {!!cvData?.educations?.length ? (
            <ul role="list" className="space-y-4">
              {cvData.educations.map((education, idx) => (
                <li key={education.id || idx}>
                  <div className="flex flex-col gap-2">
                    <div className="flex justify-between">
                      <div className="font-semibold text-gray-900">
                        {education.school}
                      </div>
                      <div className="text-sm text-gray-500">
                        {education.endYear || education.startYear}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      {[education.degree, education.field]
                        .filter(Boolean)
                        .join(' • ')}
                    </div>
                    {education.description && (
                      <p className="text-sm text-gray-600">
                        {education.description}
                      </p>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-gray-500">
              No education information available
            </p>
          )}
        </div>
      </div>

      {/* Files */}
      {!!candidate.cvFile && (
        <div className="space-y-4 rounded-2xl border border-[#c3c3c3] bg-white p-6">
          <div className="text-lg font-bold">Files</div>
          <div className="flex items-center justify-between rounded-2xl border border-gray-200 p-4">
            <div className="flex items-center gap-3">
              <Paperclip className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {candidate.cvFile.name}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Link href={candidate.cvFile.path} download target="_blank">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Eye className="h-4 w-4" />
                  View
                </Button>
              </Link>

              {/* <Link href={candidate.cvFile.path} download>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </Link> */}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {/* <div className="flex justify-end gap-4">
        <Button variant="outline" className="border-primary text-primary">
          Share CV link
        </Button>
        <Button className="bg-primary text-white">Export CV to PDF</Button>
      </div> */}
    </div>
  );
}
