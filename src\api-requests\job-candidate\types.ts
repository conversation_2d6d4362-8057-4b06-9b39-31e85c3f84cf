import { UserCV } from '@/store/user-atom';
import { Job } from '../job/types';
import { AdminSimulation } from '../simulation';
import { UserProfile } from '../user-profile';
import { User } from '../user/types';

export enum JobCandidateQueryKeys {
  GET_CANDIDATE_BY_JOB = 'getCandidateByJob',
  GET_CANDIDATE_ALL_POSITIONS_BY_ORG = 'getCandidateAllPositionsByOrg',
  GET_CANDIDATE_BY_ORG = 'getCandidateByOrg',
  GET_CANDIDATE_FOR_ADMIN = 'getCandidateForAdmin',
  GET_ORG_CANDIDATES_QUICK_ANALYTICS = 'getOrgCandidatesQuickAnalytics',
  GET_CANDIDATE_DASHBOARD_STATS_BY_JOB = 'useGetCandidateDashboardStatsByJob',
  GET_CANDIDATE_STATS_BY_JOB = 'getCandidateStatsByJob',
}

export const LIMIT = 10;

export interface OrgCandidateListParams {
  page: number;
  limit: number;
  jobId?: string;
  email?: string;
  name?: string;
  status?: string;
  orgId?: string;
  applyMode?: string;
  sort?: string;
  applicationStatus?: string;
  simulationStatus?: string;
  appliedFrom?: Date;
  appliedTo?: Date;
}

export interface OrgCandidateAdminListParams {
  page: number;
  limit: number;
  email?: string;
  candidateName?: string;
  orgName?: string;
  status?: string;
}

export interface OrgCandidateAllPositionsParams {
  orgId: string;
  candidateId: string;
}

export interface ListJobCandidateParams {
  limit?: number;
  page?: number;
  title?: string;
  location?: string;
  categories?: string;
  jobType?: string;
  salary?: string;
  level?: string;
}

export enum ApplicationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INTERVIEW = 'interview',
  OFFER = 'offer',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  HIRED = 'hired',
  CLOSED = 'closed',
  // OFFER_ACCEPTED = 'offer_accepted',
  // OFFER_DECLINED = 'offer_declined',
}

export interface JobCandidateInterviewStat {
  id: string;
  scheduledAt: Date;
  localScheduledDate: string;
  localScheduledTime: string;
  responseDeadline?: Date;
  localResponseDate?: string;
  localResponseTime?: string;
  timezone: string;
  status:
    | 'scheduled'
    | 'accepted'
    | 'declined'
    | 'completed'
    | 'canceled_by_interviewer'
    | 'canceled_by_candidate'
    | 'no_show'
    | 'no_response';
  // For interviewer only
  notes?: string;
  // For employer only
  employerNotes?: string;
  statusUpdatedAt: Date;
}

export interface JobCandidateOfferStat {
  id: string;
  title: string;
  startDate: Date;
  offerExpiresAt?: Date;
  localStartDate: string;
  localStartTime: string;
  localExpiresDate?: string;
  localExpiresTime?: string;
  timezone: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'rescinded';
  salary: { amount: number; currency: string };
  notes?: string;
  statusUpdatedAt: Date;
}

export interface JobCandidateRejectedStat {
  id: string;
  reasonCode: string;
  notes?: string;
  statusUpdatedAt: Date;
}

export interface JobCandidateHiredStat {
  id: string;
  startDate: Date;
  localStartDate: string;
  localStartTime: string;
  timezone: string;
  employmentType: string;
  statusUpdatedAt: Date;
}

export interface JobCandidate {
  _id: string;
  userId: string;
  email: string;
  simulationId: string;
  jobId?: string;
  orgId?: string;
  applicationStatus: ApplicationStatus;
  simulationStatus: 'active' | 'completed';
  // TODO: startedAt is deprecated
  startedAt: Date;
  appliedAt: Date;
  completedAt?: Date;
  scores?: number;
  matchPercentage?: number;
  performanceScore?: number;
  cvData?: UserCV;
  aiEvaluation?: {
    summary?: string;
    areasForImprovement?: any[];
    strengths?: any[];
    personalities?: { name: string; explanation: string }[];
    tasks?: {
      title: string;
      description: string;
      submissions?: any[];
      exampleSubmission?: string;
    }[];
    skills?: {
      hardSkills?: { name: string; rating: number }[];
      softSkills?: { name: string; rating: number }[];
    };
    cvProcessResult?: {
      cvData: UserCV;
      result: {
        matchPercentage: number;
        overview?: string[];
        feedback?: string[];
        strengths?: string[];
        areasForImprovement?: string[];
        tasks?: {
          title: string;
          description: string;
          exampleSubmission: string;
        }[];
      };
    };
    risks?: {
      name: string;
      level: string;
      explanation: string;
      mitigation?: string;
    }[];
  };
  tasks?: {
    id: string;
    title: string;
    description: string;
    submissions?: {
      submittedAt: Date;
      content: string;
    }[];
    submission?: {
      submittedAt: Date;
      content: string;
    };
  }[];
  // cvFile only exists when applyMode = cv
  cvFile?: {
    name: string;
    path: string;
  };
  userProfileSnapshot?: UserProfile;
  quickQuestions?: {
    id: string;
    text: string;
    type: 'text' | 'number' | 'yes_no' | 'single' | 'multiple';
    required: boolean;
    // Use for 'single' and 'multiple' type
    options?: any[];
    answer?: any;
  }[];
  hasQuickQuestions?: boolean;
  applyMode: 'cv' | 'simulation';
  user?: User;
  simulation?: AdminSimulation;
  job?: Job;
  isPrimary?: boolean;
  otherJobs?: {
    id: string;
    title: string;
    jobId?: string;
    simulationId?: string;
    isPrimary?: boolean;
    applicationStatus: ApplicationStatus;
    job?: Partial<Job>;
    simulation?: Partial<AdminSimulation>;
  }[];
  interviewStat?: JobCandidateInterviewStat;
  offerStat?: JobCandidateOfferStat;
  hiredStat?: JobCandidateHiredStat;
  rejectedStat?: JobCandidateRejectedStat;
}

export interface QuickAnalyticsResponse {
  totalCandidates: number;
  totalApplications: number;
  totalInProgressCandidates: number;
  last7DaysTotalCandidates: number;
  last30DaysTotalCandidates: number;
  totalSubmitted: number;
  totalUnderReview: number;
  totalInterview: number;
}
