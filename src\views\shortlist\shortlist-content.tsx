import { JobCandidate } from '@/api-requests/job-candidate/types';
import { Shortlist } from '@/api-requests/shortlist';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { PlusIcon } from 'lucide-react';
import { ActionIcon, Button, Checkbox } from 'rizzui';
import DeleteIcon from '../icons/delete';
import EditIcon from '../icons/edit';

interface IProps {
  shortlists: Shortlist[];
  candidate?: ShortlistCandidate | null;
  candidates?: ShortlistCandidate[];
  isLoading?: boolean;
  canUpdate?: boolean;
  onCreate: () => void;
  onEdit?: (shortlist: Shortlist) => void;
  onDelete?: (shortlist: Shortlist) => void;
  onCheckboxChange: (
    checked: boolean,
    shortlist: Shortlist,
    candidate: ShortlistCandidate | ShortlistCandidate[]
  ) => void;
}

export default function ShortlistContent({
  shortlists,
  candidate,
  isLoading,
  canUpdate,
  candidates,
  onCreate,
  onEdit,
  onDelete,
  onCheckboxChange,
}: IProps) {
  return (
    <div>
      {shortlists?.length === 0 ? (
        <div className="flex flex-col items-center gap-2">
          <img
            src="/candidate/empty-shortlist.png"
            alt="empty shortlist"
            className="h-[100px] w-[100px] object-cover"
          />
          <div className="text-lg font-bold">Add to Shortlist</div>
          <div className="w-full text-center text-gray-500 sm:w-[80%]">
            You don't have any shortlists yet. Start by creating a new one
          </div>
        </div>
      ) : (
        <>
          <div>Select your shortlist</div>
          <div className="space-y-4 rounded-2xl border border-[#c3c3c3] p-4">
            {shortlists?.map((list) => {
              const checked = (() => {
                if (candidate) {
                  return list?.shortlistCandidates?.some(
                    (item) => item.candidateId === candidate._id
                  );
                }

                if (candidates?.length && list?.shortlistCandidates?.length) {
                  const candidateIds = new Set(candidates.map((c) => c._id));
                  const shortlistIds = new Set(
                    list.shortlistCandidates.map((item) => item.candidateId)
                  );

                  return (
                    candidateIds.size === shortlistIds.size &&
                    [...candidateIds].every((id) => shortlistIds.has(id))
                  );
                }

                if (candidates?.length && !list?.shortlistCandidates?.length) {
                  return false;
                }

                return false;
              })();

              return (
                <div
                  key={list._id}
                  className="flex items-center justify-between gap-4"
                >
                  {canUpdate ? (
                    <div className="text-xs">{list.name}</div>
                  ) : (
                    <div>
                      <Checkbox
                        variant="flat"
                        size="sm"
                        label={list.name}
                        defaultChecked={checked}
                        onChange={(e) => {
                          onCheckboxChange(
                            e.target.checked,
                            list,
                            candidate as ShortlistCandidate
                          );
                        }}
                        disabled={isLoading}
                      />
                    </div>
                  )}

                  {canUpdate && (
                    <div className="flex gap-1">
                      <ActionIcon
                        size="sm"
                        variant="text"
                        onClick={() => onEdit?.(list)}
                      >
                        <EditIcon className="h-4 w-4" />
                      </ActionIcon>
                      <ActionIcon
                        size="sm"
                        variant="text"
                        onClick={() => onDelete?.(list)}
                      >
                        <DeleteIcon className="h-4 w-4" />
                      </ActionIcon>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </>
      )}

      <div className="mt-4 flex justify-center">
        <Button className="bg-primary text-white" size="sm" onClick={onCreate}>
          <PlusIcon /> <span>Create New Shortlist</span>
        </Button>
      </div>
    </div>
  );
}
