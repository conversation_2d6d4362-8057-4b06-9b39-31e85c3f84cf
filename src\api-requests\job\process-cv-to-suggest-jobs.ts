import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation } from '@tanstack/react-query';
import { Simulation } from './types';

export interface ProcessCVToSuggestJobResponse {
  _id: string;
  jobId: string;
  title: string;
  simulation: Simulation;
  matchPercentage: number;
  score: number;
}

async function processCVToSuggestJobs(params: {
  cvFile: File;
}): Promise<ProcessCVToSuggestJobResponse[] | null> {
  // await new Promise((resolve) => setTimeout(resolve, 10000)); // simulate 6s delay
  // return null;

  const formData = new FormData();

  formData.append('file', params.cvFile);

  const response = await axiosInstance.post<ProcessCVToSuggestJobResponse[]>(
    API_ENDPONTS.PROCESS_CV_TO_SUGGEST_JOBS,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
}

export const useProcessCVToSuggestJobs = () => {
  return useMutation({
    mutationFn: (params: { cvFile: File }) => processCVToSuggestJobs(params),
    // onError: (error: any) => {
    //   // let errMsg = 'Failed to process CV. Please try again later.';
    //   // if (error instanceof AxiosError && error.status === 400) {
    //   //   errMsg = error.response?.data?.message || errMsg;
    //   // }
    //   // toast.error(errMsg);
    // },
  });
};
