import cn from '@/utils/class-names';
import { useState } from 'react';
import { Button } from 'rizzui';

export const LongTextCell = ({
  text,
  lines = 3,
  textClassName,
}: {
  text: string;
  textClassName?: string;
  lines?: number;
}) => {
  const [expanded, setExpanded] = useState(false);

  const clampStyle: React.CSSProperties = expanded
    ? {}
    : {
        display: '-webkit-box',
        WebkitLineClamp: lines,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
      };

  if (!text) return null;

  return (
    <div className="relative max-w-full">
      <span
        className={cn(textClassName ?? 'text-sm text-gray-800')}
        style={clampStyle}
      >
        {text}
      </span>

      {!expanded && (
        <div className="pointer-events-none absolute inset-x-0 bottom-6 h-6 bg-gradient-to-t from-white" />
      )}

      <Button
        variant="text"
        onClick={() => setExpanded((v) => !v)}
        className="ml-1 mt-1 inline-flex !h-auto !w-auto !p-0 text-xs font-medium text-gray-600 hover:underline"
        aria-expanded={expanded}
      >
        {expanded ? 'Show less' : 'Show more'}
      </Button>
    </div>
  );
};
