import JobBanner from '@/views/banner/job-banner';
import <PERSON>Footer from '@/views/banner/job-banner/footer';

export default function LayoutProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='relative'>
      <div
        className="bg-cover bg-bottom bg-no-repeat"
        style={{
          backgroundImage: `url("/job/job-hero.jpg")`,
        }}
      >
        <JobBanner />

        <div
          className={
            'relative z-10 mx-auto flex w-full max-w-[1440px] flex-col gap-14 px-4 pb-14 xl:px-0 xl:pb-24'
          }
        >
          {children}
        </div>
      </div>

      <JobFooter />

      {/* <div
        className="absolute bottom-0 left-0 z-0 h-[220px] w-full bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: 'url("/job/job-footer-bg.png")' }}
      ></div> */}
    </div>
  );
}
