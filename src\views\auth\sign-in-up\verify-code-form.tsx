'use client';

import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { Input, Button, Loader } from 'rizzui';
import { useMemo, useRef, useState } from 'react';
import ArrowLeftIcon from '@/views/icons/arrow-left';
import { useAuthActions } from '@/hooks/use-auth-actions';
import toast from 'react-hot-toast';
import cn from '@/utils/class-names';
import { ActionType } from './sign-in-modal';

type FormValues = {
  code: string[];
  firstName?: string;
  lastName?: string;
  email: string;
  password: string;
  role: string;
  orgType: {
    type: string;
    role: string;
  };
  file?: File;
  organizationName?: string;
  description?: string;
  address?: string;
  city?: string;
  region?: string;
  country?: {
    label: string;
    value: string;
  };
  newPassword?: string;
  confirmPassword?: string;
};
interface IProps {
  onSetAction: (action: string) => void;
  onClose: () => void;
  verifyType: string;
  role: string;
}

export default function VerifyCodeForm({
  onSetAction,
  onClose,
  verifyType,
  role,
}: IProps) {
  const {
    loginVerify,
    loginRequestCode,
    signupVerify,
    signupRequestCode,
    forgotPasswordVerify,
    forgotPasswordRequestCode,
  } = useAuthActions();

  const inputsRef = useRef<Array<HTMLInputElement | null>>([]);

  const [resending, setResending] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { isSubmitting },
  } = useFormContext<FormValues>();

  const email = watch('email');
  const code = useWatch({ control, name: 'code' });
  const isComplete = useMemo(
    () =>
      Array.isArray(code) &&
      code.length === 6 &&
      code.every((c) => /^\d$/.test(c)),
    [code]
  );

  const focusIndex = (idx: number) => {
    const el = inputsRef.current[idx];
    if (el) {
      el.focus();
      el.select();
    }
  };

  const handleChange = (idx: number, v: string) => {
    const digit = v.replace(/\D/g, '').slice(0, 1);
    setValue(`code.${idx}` as const, digit, {
      shouldValidate: true,
      shouldDirty: true,
    });

    if (digit) {
      if (idx < 5) {
        focusIndex(idx + 1);
      } else {
        const firstEmpty = getValues('code')[0] === '';
        if (firstEmpty) focusIndex(0);
      }
    }
  };

  const handleKeyDown = (
    idx: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    const value = getValues(`code.${idx}` as const);
    if (e.key === 'Backspace') {
      if (value === '' && idx > 0) {
        focusIndex(idx - 1);
        setValue(`code.${idx - 1}` as const, '', {
          shouldValidate: true,
          shouldDirty: true,
        });
      } else {
        setValue(`code.${idx}` as const, '', {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
      e.preventDefault();
    } else if (e.key === 'ArrowLeft' && idx > 0) {
      focusIndex(idx - 1);
      e.preventDefault();
    } else if (e.key === 'ArrowRight' && idx < 5) {
      focusIndex(idx + 1);
      e.preventDefault();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const raw = e.clipboardData.getData('text');
    const digits = (raw.match(/\d/g) || []).slice(0, 6);
    if (digits.length === 0) return;

    const active = inputsRef.current.findIndex(
      (el) => el === document.activeElement
    );
    const start = active >= 0 ? active : 0;

    const next = [...getValues('code')];
    let i = 0;
    for (; i < digits.length; i++) {
      const pos = start + i;
      if (pos > 5) break;
      next[pos] = digits[i];
    }

    let j = 0;
    while (i < digits.length && j < start) {
      next[j] = digits[i];
      i++;
      j++;
    }

    next.forEach((d, k) =>
      setValue(`code.${k}` as const, d, {
        shouldValidate: true,
        shouldDirty: true,
      })
    );

    const lastFilledIndex = next.findLastIndex((c) => c !== '');
    if (lastFilledIndex >= 0) {
      if (lastFilledIndex === 5 && next[0] === '') focusIndex(0);
      else focusIndex(Math.min(lastFilledIndex + 1, 5));
    }
  };

  const onSubmit = async (data: FormValues) => {
    const joined = data.code.join('');
    let resp = null;

    if (verifyType === ActionType.SIGN_IN) {
      resp = await loginVerify({ email: data.email, code: joined });
    } else if (verifyType === ActionType.SIGN_UP) {
      resp = await signupVerify({ email: data.email, code: joined });
    } else if (verifyType === ActionType.RESET_PASSWORD) {
      resp = await forgotPasswordVerify({ email: data.email, code: joined });
    }

    if (
      resp?.user ||
      (verifyType === ActionType.RESET_PASSWORD && resp?.status)
    ) {
      if (verifyType === ActionType.RESET_PASSWORD) {
        toast.success(
          'Password reset successfully. Please login with your new password.'
        );
      }
      onClose();
    } else {
      toast.error(resp?.message || 'Something went wrong');
    }
  };

  const handleResendCode = async () => {
    setResending(true);
    let resp = null;
    if (verifyType === ActionType.SIGN_IN) {
      resp = await loginRequestCode(email);
    } else if (verifyType === ActionType.SIGN_UP) {
      const data = getValues();
      resp = await signupRequestCode({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
        role: data.orgType.role || role,
        file: data.file,
        organizationName: data.organizationName,
        description: data.description,
        address: data.address,
        city: data.city,
        region: data.region,
        country: data?.country?.label,
      });
    } else if (verifyType === ActionType.RESET_PASSWORD) {
      const data = getValues();
      resp = await forgotPasswordRequestCode({
        email,
        newPassword: data.newPassword as string,
        confirmPassword: data.confirmPassword as string,
      });
    }

    if (resp?.status) {
      toast.success('Verification code resent successfully.');
    } else {
      toast.error(resp?.message || 'Failed to resend code');
    }
    setResending(false);
  };

  return (
    <div className="mx-auto max-w-md space-y-8">
      <div className="space-y-3">
        <div className="text-center">
          <div className="relative flex items-center justify-center">
            <button onClick={() => onSetAction('')} className="absolute left-0">
              <ArrowLeftIcon className="h-6 w-6" />
            </button>
            <div className="text-2xl font-bold">Check your inbox</div>
          </div>

          <div className="mt-1 text-sm text-[#484848]">
            We’ve sent a 6-digit code to:{' '}
            <span className="font-semibold">{email}</span>
          </div>
        </div>

        <div className="flex items-center justify-center gap-3">
          {Array.from({ length: 6 }).map((_, idx) => (
            <Controller
              key={idx}
              name={`code.${idx}` as const}
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  ref={(el) => {
                    inputsRef.current[idx] = el;
                  }}
                  type="text"
                  inputMode="numeric"
                  autoComplete="one-time-code"
                  maxLength={1}
                  onChange={(e) => handleChange(idx, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(idx, e)}
                  onPaste={handlePaste}
                  className="size-14 h-12 w-12 p-0 text-center"
                  inputClassName="tracking-widest text-2xl text-center font-semibold p-0 px-4 focus:ring-0 caret-transparent"
                  size="lg"
                />
              )}
            />
          ))}
        </div>

        <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
          <span>Check your spam or</span>
          <button
            type="button"
            className={cn(
              'flex items-center gap-1 underline underline-offset-2 hover:opacity-80',
              resending ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
            )}
            onClick={handleResendCode}
            disabled={resending}
          >
            <span>Resend code</span>
            {resending && <Loader size="sm" className="h-4 w-4" />}
          </button>
        </div>
      </div>

      <Button
        className="w-full bg-primary text-white"
        disabled={!isComplete || isSubmitting}
        onClick={handleSubmit(onSubmit)}
        isLoading={isSubmitting}
      >
        Verify
      </Button>
    </div>
  );
}
