'use client';

import { useState } from 'react';
import { Button } from 'rizzui';
import ProfileDetailModal from './profile-detail-modal';
import { UserProfile } from '@/api-requests/user-profile';
import { useAtom } from 'jotai';
import { userAtom } from '@/store/user-atom';

interface IProps {
  refetch?: () => void;
  profile: UserProfile | null;
}

export const ProfileInfo = ({
  label,
  value,
}: {
  label: string;
  value: any;
}) => {
  return (
    <div>
      <div className="text-sm text-gray-500">{label}</div>
      <div>{!!value ? value : '-'}</div>
    </div>
  );
};

const getGenderDisplay = (gender?: string | null) => {
  if (!gender) return '-';
  switch (gender) {
    case 'male':
      return 'Male';
    case 'female':
      return 'Female';
    case 'other':
      return 'Other';
    default:
      return '';
  }
};

export const formatWorkType = (workTypes: string[] | undefined) => {
  if (!workTypes || workTypes.length === 0) return '-';
  return workTypes
    .map((type) => {
      switch (type) {
        case 'full_time':
          return 'Full-time';
        case 'part_time':
          return 'Part-time';
        case 'contract':
          return 'Contract';
        default:
          return type;
      }
    })
    .join(', ');
};

export const formatExperience = (
  experience: { min?: number; max?: number } | undefined
) => {
  if (!experience) return '-';

  const { min = 0, max = 0 } = experience;

  if (min === 0 && max === 0) return '-';

  const yearText = min <= 1 && max <= 1 ? 'year' : 'years';
  return `${min} - ${max} ${yearText}`;
};

export const formatSalary = (
  expectedSalary: { min?: number; max?: number; currency?: string } | undefined
) => {
  if (!expectedSalary) return '-';

  const { min = 0, max = 0, currency = '' } = expectedSalary;

  if (min === 0 && max === 0) return '-';

  return `${min} - ${max} ${currency}`;
};

export default function ProfileDetail({ refetch, profile }: IProps) {
  const [openModal, setOpenModal] = useState(false);
  const [user] = useAtom(userAtom);

  return (
    <>
      <div className="w-full rounded-2xl p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
        <div className="text-lg font-bold">Personal Information</div>

        <div className="mt-4 grid grid-cols-1 gap-2 sm:grid-cols-2">
          <ProfileInfo label="First name" value={user?.firstName} />
          <ProfileInfo label="Last name" value={user?.lastName} />
          <ProfileInfo
            label="Gender"
            value={getGenderDisplay(profile?.gender) || ''}
          />

          <ProfileInfo label="Address" value={profile?.address} />
          <ProfileInfo label="City" value={profile?.city} />
          <ProfileInfo label="Region" value={profile?.region} />
          <ProfileInfo label="Country" value={profile?.country} />
        </div>

        <div className="mt-4 text-lg font-bold">General Information</div>

        <div className="mt-4 grid grid-cols-1 gap-2 sm:grid-cols-2">
          <ProfileInfo label="Skill" value={profile?.skills?.join(', ')} />
          <ProfileInfo
            label="Industry"
            value={profile?.industries?.join(', ')}
          />
          <ProfileInfo label="Level" value={profile?.levels?.join(', ')} />

          <ProfileInfo
            label="Work type"
            value={formatWorkType(profile?.workTypes)}
          />
          <ProfileInfo
            label="Work place"
            value={profile?.workPlaces?.join(', ')}
          />
          <ProfileInfo
            label="Expected salary"
            value={formatSalary(profile?.expectedSalary)}
          />
          <ProfileInfo
            label="Experience"
            value={formatExperience(profile?.experience)}
          />
        </div>

        {user && user?.id === profile?.userId && (
          <div className="mt-4 flex justify-end">
            <Button
              className="bg-primary text-white"
              onClick={() => setOpenModal(true)}
              disabled={!user || user?.id !== profile?.userId}
            >
              Edit Profile
            </Button>
          </div>
        )}
      </div>

      {openModal && (
        <ProfileDetailModal
          open={openModal}
          onClose={() => setOpenModal(false)}
          refetch={refetch}
          profile={profile}
          user={user}
        />
      )}
    </>
  );
}
